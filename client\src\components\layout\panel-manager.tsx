import React, { useState, useEffect, useMemo } from 'react';
import { MultiPanelLayout, PanelType } from './multi-panel-layout';
import { usePreferences } from '@/hooks/use-preferences';
import debounce from 'lodash.debounce';
import { Note, OutlineItem, Citation } from '@/lib/types';
import { Button } from '@/components/ui/button';

interface PanelManagerProps {
  documentId?: string;
  outline: OutlineItem[];
  notes: Note[];
  activeOutlineItemId: string | null;
  content: string;
  citations: Citation[];
  notesType?: 'footnotes' | 'endnotes';
  onOutlineChange: (outline: OutlineItem[]) => void;
  onAddOutlineItem: (parentId?: string) => void;
  onImportOutline: () => void;
  onNoteCreate: (linkedOutlineId: string, options?: { type?: 'text' | 'image' | 'video' | 'file' }) => Note;
  onNoteUpdate: (note: Note, forceSaveNow?: boolean) => void;
  onNoteDelete: (noteId: string) => void;
  onLinkNoteToOutlineItem: (noteId: string, outlineItemId: string) => void;
  onDeleteOutlineItemWithNotes: (outlineItemId: string) => void;
  onFileUpload: (file: File) => Promise<string>;
  onOutlineItemSelect: (itemId: string) => void;
  onContentChange: (content: string) => void;
  onAddCitation: () => void;
  onUpdateCitation: (citation: Citation) => void;
  onDeleteCitation: (citationId: string) => void;
  onSpellCheck?: () => void;
  onGrammarCheck?: () => void;
  onNotesTypeChange?: (type: 'footnotes' | 'endnotes') => void;
  onNoteDuplicate?: (noteId: string) => void;
  onRefreshContent?: () => void;
  togglePanel: (panel: PanelType) => void;
  visiblePanels: PanelType[];
  getCurrentWriting?: () => { content: string; citations: Citation[] };
  currentUserPermissionLevel?: 'view' | 'edit' | null;
  isDocumentOwner?: boolean;
}

export function PanelManager(props: Omit<PanelManagerProps, 'togglePanel' | 'visiblePanels'>) {
  const { preferences, updatePreferences, isLoading: preferencesLoading } = usePreferences();
  const [visiblePanels, setVisiblePanels] = useState<PanelType[]>(['outline', 'writing']);
  const [poppedOutPanels, setPoppedOutPanels] = useState<PanelType[]>([]);
  const [poppedOutWindows, setPoppedOutWindows] = useState<Record<string, Window | null>>({});
  const [hasInitializedVisiblePanels, setHasInitializedVisiblePanels] = useState(false);

  const ALL_PANELS: PanelType[] = ['outline', 'writing'];

  const popOutPanel = (panel: PanelType) => {
    setPoppedOutPanels([...poppedOutPanels, panel]);
    const url = `/popout/${props.documentId}/${panel}`;
    const newWindow = window.open(url, `popout-${panel}`, 'width=800,height=600');
    setPoppedOutWindows({ ...poppedOutWindows, [panel]: newWindow });

    const timer = setInterval(() => {
      if (newWindow?.closed) {
        clearInterval(timer);
        popInPanel(panel);
      }
    }, 500);
  };

  const popInPanel = (panel: PanelType) => {
    setPoppedOutPanels(poppedOutPanels.filter(p => p !== panel));

    // Clean up any global state that might interfere with panel resizing
    // This helps prevent the panel divider drag direction issue
    document.body.style.cursor = '';
    document.documentElement.style.cursor = '';
    document.body.classList.remove('dragging-mode', 'disable-websocket-updates', 'minimap-dragging');
    document.documentElement.classList.remove('outline-dragging-active');

    // Refresh content when a popout is closed to sync any changes made in the popout
    if (props.onRefreshContent) {
      console.log(`Refreshing content after ${panel} panel was popped in`);
      // Small delay to ensure the popout window has fully closed
      setTimeout(() => {
        props.onRefreshContent!();
      }, 100);
    }
  };

  const popInAllPanels = () => {
    Object.values(poppedOutWindows).forEach(win => win?.close());
    setPoppedOutPanels([]);
    setPoppedOutWindows({});
    setVisiblePanels(ALL_PANELS);

    // Clean up any global state that might interfere with panel resizing
    // This helps prevent the panel divider drag direction issue
    document.body.style.cursor = '';
    document.documentElement.style.cursor = '';
    document.body.classList.remove('dragging-mode', 'disable-websocket-updates', 'minimap-dragging');
    document.documentElement.classList.remove('outline-dragging-active');
  };

  useEffect(() => {
    if (hasInitializedVisiblePanels || preferencesLoading) {
      return;
    }

    const preferredVisiblePanels = preferences.visiblePanels;
    let validatedPreferredPanels: PanelType[] = [];

    if (Array.isArray(preferredVisiblePanels)) {
      validatedPreferredPanels = preferredVisiblePanels.filter(
        p => p !== 'notes' && ALL_PANELS.includes(p as PanelType)
      ) as PanelType[];
    }

    let newLocalStateCandidate: PanelType[];

    if (validatedPreferredPanels.length > 0) {
      newLocalStateCandidate = validatedPreferredPanels;
    } else {
      newLocalStateCandidate = [...ALL_PANELS];
    }

    if (newLocalStateCandidate.length === 0) {
      newLocalStateCandidate = ['outline'];
    }

    if (JSON.stringify(visiblePanels) !== JSON.stringify(newLocalStateCandidate)) {
      setVisiblePanels(newLocalStateCandidate);
    }
    setHasInitializedVisiblePanels(true);
  }, [preferences.visiblePanels, preferencesLoading, visiblePanels, hasInitializedVisiblePanels]);

  const debouncedUpdateVisibility = useMemo(() =>
    debounce((newPanels: PanelType[], currentPrefs: typeof preferences) => {
      updatePreferences({ ...currentPrefs, visiblePanels: newPanels }, false);
    }, 500),
    [updatePreferences]
  );

  const togglePanel = (panel: PanelType) => {
    let newVisiblePanelsArray: PanelType[];
    if (visiblePanels.includes(panel)) {
      if (visiblePanels.length === 1) {
        return;
      }
      newVisiblePanelsArray = visiblePanels.filter(p => p !== panel);
    } else {
      const tempPanels: PanelType[] = [];
      ALL_PANELS.forEach(p => {
        if (visiblePanels.includes(p) || p === panel) {
          tempPanels.push(p);
        }
      });
      newVisiblePanelsArray = tempPanels;
    }
    setVisiblePanels(newVisiblePanelsArray);
    debouncedUpdateVisibility(newVisiblePanelsArray, preferences);
  };

  if (poppedOutPanels.length === ALL_PANELS.length) {
    return (
      <div className="h-full w-full flex flex-col items-center justify-center bg-muted/50">
        <div className="text-center">
          <h3 className="text-lg font-medium text-foreground">All panels have been popped out.</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Close the pop-out windows or click the button below to return them to the main window.
          </p>
          <Button onClick={popInAllPanels} className="mt-4">
            Return Panels
          </Button>
        </div>
      </div>
    );
  }

  return (
    <MultiPanelLayout
      {...props}
      visiblePanels={visiblePanels.filter(p => !poppedOutPanels.includes(p))}
      popOutPanel={popOutPanel}
    />
  );
}
