import * as Diff from 'diff';

// Import types from the shared schema
interface OutlineItem {
  id: string;
  title: string;
  children?: OutlineItem[];
  number?: string;
}

interface Note {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  linkedOutlineId: string;
  linkedOutlineNumber?: string;
  position?: number;
  imageUrls?: string[];
  videoUrls?: string[];
  fileUrls?: string[];
  type?: 'text' | 'image' | 'video' | 'file';
  primaryAssetUrl?: string;
}

interface WritingSection {
  content: string;
  citations?: Array<{
    id: string;
    reference: string;
    marker: string;
  }>;
}

interface DocumentContentData {
  outline: OutlineItem[];
  notes: Note[];
  writing: Record<string, WritingSection>;
}

export interface DiffLine {
  type: 'added' | 'removed' | 'unchanged' | 'context';
  content: string;
  lineNumber?: {
    old?: number;
    new?: number;
  };
}

export interface SectionDiff {
  type: 'outline' | 'note' | 'writing';
  id: string;
  title: string;
  action: 'added' | 'removed' | 'modified' | 'unchanged';
  lines: DiffLine[];
  addedLines: number;
  removedLines: number;
}

export interface DocumentDiff {
  addedLines: number;
  removedLines: number;
  sections: SectionDiff[];
}

// Helper function to convert HTML content to plain text for diffing
function htmlToPlainText(html: string): string {
  if (!html) return '';
  
  // Remove HTML tags and convert common elements to readable text
  return html
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n')
    .replace(/<p[^>]*>/gi, '')
    .replace(/<\/li>/gi, '\n')
    .replace(/<li[^>]*>/gi, '• ')
    .replace(/<\/ul>/gi, '\n')
    .replace(/<ul[^>]*>/gi, '')
    .replace(/<\/ol>/gi, '\n')
    .replace(/<ol[^>]*>/gi, '')
    .replace(/<[^>]*>/g, '') // Remove all other HTML tags
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .trim();
}

// Create a diff for text content
function createTextDiff(oldText: string, newText: string, contextLines: number = 3): DiffLine[] {
  const oldLines = oldText.split('\n');
  const newLines = newText.split('\n');
  
  const diff = Diff.diffLines(oldText, newText);
  const lines: DiffLine[] = [];
  
  let oldLineNum = 1;
  let newLineNum = 1;
  
  for (const part of diff) {
    const partLines = part.value.split('\n');
    // Remove the last empty line if it exists (from split)
    if (partLines[partLines.length - 1] === '') {
      partLines.pop();
    }
    
    for (const line of partLines) {
      if (part.added) {
        lines.push({
          type: 'added',
          content: line,
          lineNumber: { new: newLineNum++ }
        });
      } else if (part.removed) {
        lines.push({
          type: 'removed',
          content: line,
          lineNumber: { old: oldLineNum++ }
        });
      } else {
        lines.push({
          type: 'unchanged',
          content: line,
          lineNumber: { old: oldLineNum++, new: newLineNum++ }
        });
      }
    }
  }
  
  return lines;
}

// Flatten outline items for comparison
function flattenOutlineItems(items: OutlineItem[]): OutlineItem[] {
  const result: OutlineItem[] = [];
  
  function traverse(items: OutlineItem[], depth: number = 0) {
    for (const item of items) {
      result.push({ ...item, depth } as OutlineItem & { depth: number });
      if (item.children && item.children.length > 0) {
        traverse(item.children, depth + 1);
      }
    }
  }
  
  traverse(items);
  return result;
}

// Create outline diff
function createOutlineDiff(oldOutline: OutlineItem[], newOutline: OutlineItem[]): SectionDiff[] {
  const oldFlat = flattenOutlineItems(oldOutline);
  const newFlat = flattenOutlineItems(newOutline);
  const sections: SectionDiff[] = [];
  
  // Track processed items
  const processedOld = new Set<string>();
  const processedNew = new Set<string>();
  
  // Find added and modified items
  for (const newItem of newFlat) {
    const oldItem = oldFlat.find(item => item.id === newItem.id);
    
    if (!oldItem) {
      // Added item
      const depth = (newItem as any).depth || 0;
      const indent = '  '.repeat(depth);
      sections.push({
        type: 'outline',
        id: newItem.id,
        title: `Outline: ${newItem.title}`,
        action: 'added',
        lines: [{
          type: 'added',
          content: `${indent}${newItem.number}. ${newItem.title}`
        }],
        addedLines: 1,
        removedLines: 0
      });
    } else if (oldItem.title !== newItem.title || oldItem.number !== newItem.number) {
      // Modified item
      const oldDepth = (oldItem as any).depth || 0;
      const newDepth = (newItem as any).depth || 0;
      const oldIndent = '  '.repeat(oldDepth);
      const newIndent = '  '.repeat(newDepth);
      
      sections.push({
        type: 'outline',
        id: newItem.id,
        title: `Outline: ${newItem.title}`,
        action: 'modified',
        lines: [
          {
            type: 'removed',
            content: `${oldIndent}${oldItem.number}. ${oldItem.title}`
          },
          {
            type: 'added',
            content: `${newIndent}${newItem.number}. ${newItem.title}`
          }
        ],
        addedLines: 1,
        removedLines: 1
      });
      processedOld.add(oldItem.id);
    }
    processedNew.add(newItem.id);
  }
  
  // Find removed items
  for (const oldItem of oldFlat) {
    if (!processedOld.has(oldItem.id) && !newFlat.some(item => item.id === oldItem.id)) {
      const depth = (oldItem as any).depth || 0;
      const indent = '  '.repeat(depth);
      sections.push({
        type: 'outline',
        id: oldItem.id,
        title: `Outline: ${oldItem.title}`,
        action: 'removed',
        lines: [{
          type: 'removed',
          content: `${indent}${oldItem.number}. ${oldItem.title}`
        }],
        addedLines: 0,
        removedLines: 1
      });
    }
  }
  
  return sections;
}

// Create notes diff
function createNotesDiff(oldNotes: Note[], newNotes: Note[]): SectionDiff[] {
  const sections: SectionDiff[] = [];
  const processedOld = new Set<string>();
  const processedNew = new Set<string>();
  
  // Find added and modified notes
  for (const newNote of newNotes) {
    const oldNote = oldNotes.find(note => note.id === newNote.id);
    
    if (!oldNote) {
      // Added note
      const content = htmlToPlainText(newNote.content || '');
      const lines = content.split('\n').map(line => ({
        type: 'added' as const,
        content: line
      }));
      
      sections.push({
        type: 'note',
        id: newNote.id,
        title: `Note: ${newNote.title || 'Untitled'}`,
        action: 'added',
        lines,
        addedLines: lines.length,
        removedLines: 0
      });
    } else {
      // Check for modifications
      const titleChanged = oldNote.title !== newNote.title;
      const contentChanged = oldNote.content !== newNote.content;
      
      if (titleChanged || contentChanged) {
        const lines: DiffLine[] = [];
        let addedLines = 0;
        let removedLines = 0;
        
        if (titleChanged) {
          lines.push({
            type: 'removed',
            content: `Title: ${oldNote.title || 'Untitled'}`
          });
          lines.push({
            type: 'added',
            content: `Title: ${newNote.title || 'Untitled'}`
          });
          addedLines++;
          removedLines++;
        }
        
        if (contentChanged) {
          const oldContent = htmlToPlainText(oldNote.content || '');
          const newContent = htmlToPlainText(newNote.content || '');
          const contentDiff = createTextDiff(oldContent, newContent);
          lines.push(...contentDiff);
          
          addedLines += contentDiff.filter(line => line.type === 'added').length;
          removedLines += contentDiff.filter(line => line.type === 'removed').length;
        }
        
        sections.push({
          type: 'note',
          id: newNote.id,
          title: `Note: ${newNote.title || 'Untitled'}`,
          action: 'modified',
          lines,
          addedLines,
          removedLines
        });
        processedOld.add(oldNote.id);
      }
    }
    processedNew.add(newNote.id);
  }
  
  // Find removed notes
  for (const oldNote of oldNotes) {
    if (!processedOld.has(oldNote.id) && !newNotes.some(note => note.id === oldNote.id)) {
      const content = htmlToPlainText(oldNote.content || '');
      const lines = content.split('\n').map(line => ({
        type: 'removed' as const,
        content: line
      }));
      
      sections.push({
        type: 'note',
        id: oldNote.id,
        title: `Note: ${oldNote.title || 'Untitled'}`,
        action: 'removed',
        lines,
        addedLines: 0,
        removedLines: lines.length
      });
    }
  }
  
  return sections;
}

// Helper function to find outline item title by ID
function findOutlineItemTitle(outline: OutlineItem[], itemId: string): string | null {
  function searchRecursive(items: OutlineItem[]): string | null {
    for (const item of items) {
      if (item.id === itemId) {
        return item.title;
      }
      if (item.children && item.children.length > 0) {
        const found = searchRecursive(item.children);
        if (found) return found;
      }
    }
    return null;
  }
  return searchRecursive(outline);
}

// Create writing sections diff
function createWritingDiff(
  oldWriting: Record<string, WritingSection>,
  newWriting: Record<string, WritingSection>,
  outline: OutlineItem[] = []
): SectionDiff[] {
  const sections: SectionDiff[] = [];
  const allSectionIds = new Set([...Object.keys(oldWriting), ...Object.keys(newWriting)]);
  
  for (const sectionId of allSectionIds) {
    const oldSection = oldWriting[sectionId];
    const newSection = newWriting[sectionId];

    // Get the outline item title for this writing section
    const outlineTitle = findOutlineItemTitle(outline, sectionId);
    const sectionTitle = outlineTitle ? `Writing: ${outlineTitle}` : `Writing Section: ${sectionId}`;

    if (!oldSection && newSection) {
      // Added section
      const content = htmlToPlainText(newSection.content || '');
      const lines = content.split('\n').map(line => ({
        type: 'added' as const,
        content: line
      }));

      sections.push({
        type: 'writing',
        id: sectionId,
        title: sectionTitle,
        action: 'added',
        lines,
        addedLines: lines.length,
        removedLines: 0
      });
    } else if (oldSection && !newSection) {
      // Removed section
      const content = htmlToPlainText(oldSection.content || '');
      const lines = content.split('\n').map(line => ({
        type: 'removed' as const,
        content: line
      }));

      sections.push({
        type: 'writing',
        id: sectionId,
        title: sectionTitle,
        action: 'removed',
        lines,
        addedLines: 0,
        removedLines: lines.length
      });
    } else if (oldSection && newSection && oldSection.content !== newSection.content) {
      // Modified section
      const oldContent = htmlToPlainText(oldSection.content || '');
      const newContent = htmlToPlainText(newSection.content || '');
      const lines = createTextDiff(oldContent, newContent);

      const addedLines = lines.filter(line => line.type === 'added').length;
      const removedLines = lines.filter(line => line.type === 'removed').length;

      sections.push({
        type: 'writing',
        id: sectionId,
        title: sectionTitle,
        action: 'modified',
        lines,
        addedLines,
        removedLines
      });
    }
  }
  
  return sections;
}

// Main function to compute comprehensive document diff
export function computeDocumentDiff(
  currentContent: DocumentContentData,
  previousContent: DocumentContentData
): DocumentDiff {
  const sections: SectionDiff[] = [];
  
  // Compare outline
  const outlineDiff = createOutlineDiff(
    previousContent.outline || [],
    currentContent.outline || []
  );
  sections.push(...outlineDiff);
  
  // Compare notes
  const notesDiff = createNotesDiff(
    previousContent.notes || [],
    currentContent.notes || []
  );
  sections.push(...notesDiff);
  
  // Compare writing sections
  const writingDiff = createWritingDiff(
    previousContent.writing || {},
    currentContent.writing || {},
    currentContent.outline || []
  );
  sections.push(...writingDiff);
  
  // Calculate totals
  const addedLines = sections.reduce((sum, section) => sum + section.addedLines, 0);
  const removedLines = sections.reduce((sum, section) => sum + section.removedLines, 0);
  
  return {
    addedLines,
    removedLines,
    sections
  };
}
