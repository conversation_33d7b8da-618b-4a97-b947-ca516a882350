import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { NewDocumentWizard, DocumentFormat, NewDocumentFormData } from '@/components/NewDocumentWizard'; // Import the wizard, DocumentFormat, and NewDocumentFormData
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Document } from "@shared/schema";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Plus, FileText, Calendar, Trash2, Share2, MoreHorizontal, Copy, ExternalLink, Search, LogOut } from "lucide-react"; // Added LogOut
import { format } from "date-fns";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { ShareModal } from "@/components/share-modal";
import { Badge } from "@/components/ui/badge";
import { AppHeader } from "@/components/layout/app-header";
import "remixicon/fonts/remixicon.css";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ReferenceManagerModal } from "@/components/reference";

export default function DocumentsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [_, navigate] = useLocation();
  const { user, logoutMutation } = useAuth();
  const { toast } = useToast();
  const [isWizardOpen, setIsWizardOpen] = useState(false); // New state for wizard
  const [isReferencesModalOpen, setIsReferencesModalOpen] = useState(false);
  
  // State for tracking which document to share
  const [shareDocument, setShareDocument] = useState<Document | null>(null);

  // Define types for the dashboard data
  type SharedDocumentInfo = {
    documentId: string;
    title: string;
    ownerUsername: string;
    permissionLevel: 'view' | 'edit';
    sharedAt: string; // Dates are typically strings in JSON responses
  };

  type DashboardData = {
    owned: Document[];
    shared: SharedDocumentInfo[];
  };

  // Fetch all documents for the user (owned and shared)
  const { data: dashboardData, isLoading, error } = useQuery<DashboardData>({
    queryKey: ["/api/documents"],
    queryFn: async () => {
      const res = await fetch("/api/documents");
      if (!res.ok) {
        // Attempt to parse error response if possible
        try {
          const errData = await res.json();
          throw new Error(errData.message || `Failed to fetch documents: ${res.statusText}`);
        } catch {
          throw new Error(`Failed to fetch documents: ${res.statusText}`);
        }
      }
      return res.json();
    },
  });

  // Fetch pending access requests
  const { data: accessRequests, isLoading: isLoadingAccessRequests, error: errorAccessRequests } = useQuery({
    queryKey: ["/api/access-requests"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/access-requests");
      if (!res.ok) {
        throw new Error("Failed to fetch access requests");
      }
      return res.json();
    },
  });

  const ownedDocuments = dashboardData?.owned ?? [];
  const sharedDocuments = dashboardData?.shared ?? [];

  // Handle creating a new document
  const createDocumentMutation = useMutation({
    mutationFn: async (data: { title: string; format?: DocumentFormat }) => { // Expect title and optional format
      // Send both title and format to the backend
      const res = await apiRequest("POST", "/api/documents", { title: data.title, format: data.format });
      return await res.json();
    },
    onSuccess: (newDocument: Document) => {
      queryClient.invalidateQueries({ queryKey: ["/api/documents"] });
      navigate(`/document/${newDocument.id}`);
      toast({ // Added toast for better UX
        title: "Success",
        description: `Document "${newDocument.title || 'Untitled Document'}" created.`,
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create a new document",
        variant: "destructive",
      });
    },
  });

  const updateAccessRequestMutation = useMutation({
    mutationFn: async ({ requestId, status, permissionLevel }: { requestId: number; status: 'approved' | 'denied', permissionLevel?: 'view' | 'edit' }) => {
      await apiRequest("PUT", `/api/access-requests/${requestId}`, { status, permissionLevel });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/access-requests"] });
      queryClient.invalidateQueries({ queryKey: ["/api/documents"] }); // Invalidate documents to update share info
      toast({
        title: "Success",
        description: "Access request updated.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update access request.",
        variant: "destructive",
      });
    },
  });

  // Handle leaving a document share
  const leaveShareMutation = useMutation({
    mutationFn: async (documentId: string) => {
      await apiRequest("DELETE", `/api/documents/${documentId}/shares/me`);
      return documentId;
    },
    onSuccess: (documentId: string) => {
      queryClient.invalidateQueries({ queryKey: ["/api/documents"] });
      toast({
        title: "Success",
        description: "You have left the shared document.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to leave the shared document.",
        variant: "destructive",
      });
    },
  });

  // Handle deleting a document
  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: string) => { // Changed to string
      await apiRequest("DELETE", `/api/documents/${documentId}`);
      return documentId;
    },
    onSuccess: (documentId: string) => { // Changed to string
      queryClient.invalidateQueries({ queryKey: ["/api/documents"] });
      toast({
        title: "Success",
        description: "Document deleted successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete the document",
        variant: "destructive",
      });
    },
  });

  const handleCreateNewDocument = () => {
    // createDocumentMutation.mutate("Untitled Document"); // Old behavior
    setIsWizardOpen(true); // Open the wizard
  };

  const handleDeleteDocument = (id: string) => { // Changed to string
    if (window.confirm("Are you sure you want to delete this document?")) {
      deleteDocumentMutation.mutate(id);
    }
  };

  const handleLeaveShare = (documentId: string) => {
    if (window.confirm("Are you sure you want to leave this shared document?")) {
      leaveShareMutation.mutate(documentId);
    }
  };

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const handleWizardClose = () => {
    setIsWizardOpen(false);
  };

  const handleWizardComplete = (formData: NewDocumentFormData) => { 
    console.log('Wizard completed with data:', formData);
    if (!formData.format) {
      toast({
        title: "Error",
        description: "Document format is required.",
        variant: "destructive",
      });
      return; // Prevent submission if format is missing
    }
    createDocumentMutation.mutate({
      title: formData.title || "Untitled Document", // Use title from wizard or default
      format: formData.format, // Pass the selected format
    });
    setIsWizardOpen(false); // Close wizard after initiating creation
  };

  // Filter documents based on search term
  const filterAndSortOwnedDocuments = () => {
    if (!ownedDocuments) return [];
    
    let filteredDocs = searchTerm.trim() !== ""
      ? ownedDocuments.filter((doc) =>
          doc.title.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : ownedDocuments;
    
    // Sort by updatedAt (most recent first)
    filteredDocs = [...filteredDocs].sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
    
    return filteredDocs;
  };

  // TODO: Consider if shared documents also need search/sort, and how to display them.
  // For now, shared documents will be listed without additional client-side filtering by this function.

  const handleViewReferences = () => setIsReferencesModalOpen(true);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-[#1e1e1e] flex flex-col">
      {/* Header */}
      <AppHeader showUserMenu={true} showDocActions={true} onReferences={handleViewReferences} onDashboard={() => navigate('/dashboard')} />

      {/* Main Content */}
      <main className="flex-1 container mx-auto py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h2 className="text-2xl font-bold mb-2 text-foreground">Documents</h2>
            <p className="text-muted-foreground">Manage and access your writing projects</p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
            <div className="relative w-full sm:w-64 flex items-center">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none">
                <Search className="h-4 w-4 text-muted-foreground" />
              </div>
              <Input
                type="text"
                placeholder="Search documents..."
                style={{ paddingLeft: "2.5rem" }}
                className="w-full rounded-md pr-3 h-10 bg-background text-foreground border border-input focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <Button 
              onClick={handleCreateNewDocument} 
              disabled={createDocumentMutation.isPending}
              className="w-full sm:w-auto"
            >
              {createDocumentMutation.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Plus className="mr-2 h-4 w-4" />
              )}
              New Document
            </Button>
          </div>
        </div>

        <Tabs defaultValue="my-documents">
          <TabsList>
            <TabsTrigger value="my-documents">My Documents</TabsTrigger>
            <TabsTrigger value="shared-with-me">Shared With Me</TabsTrigger>
            <TabsTrigger value="access-requests">Access Requests</TabsTrigger>
          </TabsList>
          <TabsContent value="my-documents">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-red-500">{error instanceof Error ? error.message : "Failed to load documents"}</p>
              </div>
            ) : ownedDocuments.length === 0 ? (
              <div className="bg-background rounded-lg border border-border p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-medium mb-2 text-foreground">No documents yet</h3>
                <p className="text-muted-foreground mb-6">Create your first document to get started</p>
                <Button onClick={handleCreateNewDocument}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Document
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                {filterAndSortOwnedDocuments().map((document: Document) => (
                  <Card key={document.id} className="flex flex-col h-full hover:shadow-md transition-shadow bg-card text-card-foreground">
                    <CardHeader className="pb-2">
                      <CardTitle className="line-clamp-2">
                        {document.title || "Untitled Document"}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <div className="space-y-1">
                        <div className="flex items-center text-muted-foreground text-sm">
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>Created {format(new Date(document.createdAt), "MMM d, yyyy")}</span>
                        </div>
                        <div className="flex items-center justify-between text-muted-foreground text-xs">
                          <span className="ml-6">Updated {format(new Date(document.updatedAt), "MMM d, yyyy")}</span>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setShareDocument(document)}
                              className="text-muted-foreground hover:text-primary"
                              title="Share document"
                            >
                              <Share2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteDocument(document.id)}
                              className="text-muted-foreground hover:text-destructive"
                              title="Delete document"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/document/${document.id}`)}
                            >
                              Open
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="shared-with-me">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-red-500">{error instanceof Error ? error.message : "Failed to load documents"}</p>
              </div>
            ) : sharedDocuments.length === 0 ? (
              <div className="bg-background rounded-lg border border-border p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-medium mb-2 text-foreground">No documents shared with you</h3>
                <p className="text-muted-foreground mb-6">Documents shared with you will appear here.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                {sharedDocuments.map((sharedDoc: SharedDocumentInfo) => (
                  <Card key={sharedDoc.documentId} className="flex flex-col h-full hover:shadow-md transition-shadow bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800 text-card-foreground">
                    <CardHeader className="pb-2">
                      <CardTitle className="line-clamp-2">
                        {sharedDoc.title || "Untitled Document"}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <p className="text-sm text-muted-foreground mb-1">Shared by: {sharedDoc.ownerUsername}</p>
                      <p className="text-sm text-muted-foreground mb-2">
                        Permissions: <Badge variant={sharedDoc.permissionLevel === 'edit' ? "default" : "secondary"}>
                          {sharedDoc.permissionLevel === 'edit' ? 'Can edit' : 'View only'}
                        </Badge>
                      </p>
                      <div className="flex items-center justify-between text-muted-foreground text-xs">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1.5" />
                          <span>Shared on {format(new Date(sharedDoc.sharedAt), "MMM d, yyyy")}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleLeaveShare(sharedDoc.documentId)} // Activated onClick
                            className="text-muted-foreground hover:text-destructive"
                            title="Leave share"
                          >
                          <LogOut className="h-4 w-4" /> {/* Replaced ExternalLink with LogOut */}
                        </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/document/${sharedDoc.documentId}`)} // TODO: Navigate to a shared document view if different
                          >
                            Open
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="access-requests">
            {isLoadingAccessRequests ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : errorAccessRequests ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-red-500">{errorAccessRequests instanceof Error ? errorAccessRequests.message : "Failed to load access requests"}</p>
              </div>
            ) : accessRequests && accessRequests.length === 0 ? (
              <div className="bg-background rounded-lg border border-border p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-medium mb-2 text-foreground">No pending access requests</h3>
                <p className="text-muted-foreground mb-6">Requests to access your documents will appear here.</p>
              </div>
            ) : (
              <div className="space-y-4 mt-6">
                {accessRequests?.map((request: any) => (
                  <Card key={request.id}>
                    <CardHeader>
                      <CardTitle>{request.requestingUsername} requests access to {request.documentTitle}</CardTitle>
                      <CardDescription>
                        Requested on {format(new Date(request.createdAt), "MMM d, yyyy")}
                      </CardDescription>
                    </CardHeader>
                    <CardFooter className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => updateAccessRequestMutation.mutate({ requestId: request.id, status: 'denied' })}>Deny</Button>
                      <Button onClick={() => updateAccessRequestMutation.mutate({ requestId: request.id, status: 'approved', permissionLevel: 'view' })}>Approve as Viewer</Button>
                      <Button onClick={() => updateAccessRequestMutation.mutate({ requestId: request.id, status: 'approved', permissionLevel: 'edit' })}>Approve as Editor</Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>
      
      {/* Share Modal */}
      {shareDocument && (
        <ShareModal
          isOpen={!!shareDocument}
          onClose={() => setShareDocument(null)}
          documentId={shareDocument.id}
          documentTitle={shareDocument.title}
        />
      )}

      <NewDocumentWizard
        isOpen={isWizardOpen}
        onClose={handleWizardClose}
        onComplete={handleWizardComplete}
      />

      <ReferenceManagerModal
        isOpen={isReferencesModalOpen}
        onOpenChange={setIsReferencesModalOpen}
        showTrigger={false}
        onAddReference={() => {}}
        onUpdateReference={() => {}}
        onDeleteReference={() => {}}
        allReferences={[]}
      />
    </div>
  );
}