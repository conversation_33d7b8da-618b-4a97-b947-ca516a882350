import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth, LoginData, RegisterData } from "@/hooks/use-auth";
import { useSystemStatus } from "@/hooks/use-system-status";
import { useLocation } from "wouter";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { insertUserSchema } from "@shared/schema";
import { Loader2, AlertTriangle } from "lucide-react";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

const loginSchema = z.object({
  username: z.string().min(1, "Email or username is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

const registerSchema = insertUserSchema.extend({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords must match",
  path: ["confirmPassword"]
});

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState("login");
  const { user, loginMutation, registerMutation, isLoading } = useAuth();
  const { data: systemStatus, isLoading: statusLoading } = useSystemStatus();
  const [location, navigate] = useLocation();

  // If already logged in, redirect to home or to the redirect URL
  useEffect(() => {
    if (user) {
      const searchParams = new URLSearchParams(location.search);
      const redirectUrl = searchParams.get('redirect');
      if (redirectUrl) {
        navigate(decodeURIComponent(redirectUrl));
      } else {
        navigate("/");
      }
    }
  }, [user, navigate, location]);

  // Set default tab based on available options
  useEffect(() => {
    if (systemStatus) {
      if (!systemStatus.loginEnabled && systemStatus.registrationEnabled) {
        setActiveTab("register");
      } else if (systemStatus.loginEnabled && !systemStatus.registrationEnabled) {
        setActiveTab("login");
      }
    }
  }, [systemStatus]);

  const loginForm = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const registerForm = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const onLoginSubmit = (data: z.infer<typeof loginSchema>) => {
    loginMutation.mutate(data as LoginData);
  };

  const onRegisterSubmit = (data: z.infer<typeof registerSchema>) => {
    // Remove confirmPassword before submission
    const { confirmPassword, ...registerData } = data;
    registerMutation.mutate(registerData as RegisterData);
  };

  // If still loading or already authenticated, show loader
  if (isLoading || user || statusLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-10 w-10 animate-spin text-primary" />
      </div>
    );
  }

  // Check if both login and registration are disabled
  const bothDisabled = systemStatus && !systemStatus.loginEnabled && !systemStatus.registrationEnabled;

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-8">
      <Card className="w-full max-w-md shadow-lg">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">Welcome to Inspire</CardTitle>
            <CardDescription className="text-center">
              Please sign in to start crafting professional papers with ease
            </CardDescription>
          </CardHeader>
          <CardContent>
            {bothDisabled ? (
              // Show message when both login and registration are disabled
              <div className="text-center py-8">
                <div className="mb-4">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-orange-100 flex items-center justify-center">
                    <AlertTriangle className="h-8 w-8 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Service Temporarily Unavailable
                  </h3>
                  <p className="text-gray-600 mb-4">
                    We apologize for the temporary inconvenience. Both login and registration are currently disabled for maintenance.
                  </p>
                  <p className="text-sm text-gray-500 mb-6">
                    Please check back later or contact support if you need immediate assistance.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => navigate("/")}
                    className="mx-auto"
                  >
                    Back to Home
                  </Button>
                </div>
              </div>
            ) : (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                {/* Only show tabs if more than one option is available */}
                {systemStatus?.loginEnabled && systemStatus?.registrationEnabled && (
                  <TabsList className="grid w-full grid-cols-2 mb-6">
                    <TabsTrigger value="login">Login</TabsTrigger>
                    <TabsTrigger value="register">Register</TabsTrigger>
                  </TabsList>
                )}

                {/* Show single option header if only one is available */}
                {systemStatus?.loginEnabled && !systemStatus?.registrationEnabled && (
                  <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold">Sign In</h3>
                    <p className="text-sm text-muted-foreground">Registration is currently disabled</p>
                  </div>
                )}

                {!systemStatus?.loginEnabled && systemStatus?.registrationEnabled && (
                  <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold">Create Account</h3>
                    <p className="text-sm text-muted-foreground">Login is currently disabled</p>
                  </div>
                )}

              {/* Login Form - only show if login is enabled */}
              {systemStatus?.loginEnabled && (
                <TabsContent value="login">
                <Form {...loginForm}>
                  <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                    <FormField
                      control={loginForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email or Username</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Enter your email or username" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={loginForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Password</FormLabel>
                          <FormControl>
                            <PasswordInput {...field} placeholder="Enter your password" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button 
                      type="submit" 
                      className="w-full" 
                      disabled={loginMutation.isPending}
                    >
                      {loginMutation.isPending ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      Login
                    </Button>
                  </form>
                </Form>
                </TabsContent>
              )}

              {/* Register Form - only show if registration is enabled */}
              {systemStatus?.registrationEnabled && (
                <TabsContent value="register">
                <Form {...registerForm}>
                  <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                    <FormField
                      control={registerForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Username</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Choose a username" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={registerForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input {...field} type="email" placeholder="Enter your email address" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={registerForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Password</FormLabel>
                          <FormControl>
                            <PasswordInput {...field} placeholder="Create a password" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={registerForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Confirm Password</FormLabel>
                          <FormControl>
                            <PasswordInput {...field} placeholder="Confirm your password" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button 
                      type="submit" 
                      className="w-full" 
                      disabled={registerMutation.isPending}
                    >
                      {registerMutation.isPending ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      Register
                    </Button>
                  </form>
                </Form>
                </TabsContent>
              )}
              </Tabs>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <div className="text-center text-sm text-muted-foreground">
              By continuing, you agree to our Terms of Service and Privacy Policy.
            </div>
          </CardFooter>
        </Card>
    </div>
  );
}