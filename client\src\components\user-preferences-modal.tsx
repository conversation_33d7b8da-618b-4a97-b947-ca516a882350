import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { UserPreferences } from '@shared/schema';
import { useAuth } from '@/hooks/use-auth';
import { usePreferences } from '@/hooks/use-preferences';
import { useUpdateUserProfile } from '@/hooks/use-user-profile';
import { format } from 'date-fns';

// Constants for form options
const colorSchemeOptions = ["Light", "Dark"];
const fontFaceOptions = ["Aptos", "Arial", "Calibri", "Times New Roman", "Georgia"];
const fontScaleOptions = ["75%", "90%", "100%", "110%", "125%", "150%"];
const vocalPitchOptions = ["Soprano", "Alto", "Tenor", "Baritone", "Bass"];
const localeOptions = ["US Canada English", "UK English", "Australian English", "Indian English"];
const speechStyleOptions = ["Precise and articulate", "Casual and relaxed", "Energetic", "Professional"];

// Paragraph formatting options
const paragraphIndentOptions = ["0", "0.5em", "1em", "1.5em", "2em"];
const lineSpacingOptions = ["1.0", "1.15", "1.5", "2.0", "2.5"];
const paragraphSpacingOptions = ["0", "0.25em", "0.5em", "1em", "1.5em"];

interface UserPreferencesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function UserPreferencesModal({ isOpen, onClose }: UserPreferencesModalProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const { preferences: globalPreferences, updatePreferences: savePreferences, speakText } = usePreferences();
  const updateProfileMutation = useUpdateUserProfile();

  // State management
  const [isSaving, setIsSaving] = useState(false);
  const [localPreferences, setLocalPreferences] = useState<UserPreferences>({
    colorScheme: "Light",
    customThemeColor: "#3b82f6",
    fontFaceLabels: "Aptos",
    fontScaleLabels: "100%",
    fontFaceUserText: "Aptos",
    fontScaleUserText: "100%",
    textToSpeechVocalPitch: "Baritone",
    textToSpeechLocale: "US Canada English",
    textToSpeechStyle: "Precise and articulate",
    subscriberNameOptions: {
      title: true,
      firstName: true,
      middleName: true,
      lastName: true,
      suffix: true,
    },
    // Paragraph formatting preferences
    paragraphIndent: "0",
    lineSpacing: "1.15",
    paragraphSpacing: "0.5em",
  });

  // Profile state
  const [email, setEmail] = useState(user?.email || '');
  const [isEmailChanged, setIsEmailChanged] = useState(false);

  // Text-to-speech state
  const [sampleText, setSampleText] = useState("This is a sample text that demonstrates how the text-to-speech feature will sound with your current settings.");

  // Refs
  const originalPreferences = useRef<UserPreferences | null>(null);

  // Initialize preferences when modal opens
  useEffect(() => {
    if (isOpen && globalPreferences) {
      originalPreferences.current = {...globalPreferences};
      setLocalPreferences({...globalPreferences});
    }
    if (isOpen && user) {
      setEmail(user.email || '');
      setIsEmailChanged(false);
    }
  }, [isOpen, globalPreferences, user]);

  // Handle modal close
  const handleClose = (open: boolean) => {
    if (!open && originalPreferences.current) {
      savePreferences(originalPreferences.current, true);
      setLocalPreferences({...originalPreferences.current});
      setEmail(user?.email || '');
      setIsEmailChanged(false);
    }
    onClose();
  };

  // Handle saving all changes
  const handleSave = async () => {
    setIsSaving(true);

    try {
      // Save preferences
      savePreferences(localPreferences, true);
      originalPreferences.current = {...localPreferences};

      // Save email if changed
      if (isEmailChanged) {
        await updateProfileMutation.mutateAsync({ email: email || null });
        setIsEmailChanged(false);
      }

      setTimeout(() => {
        setIsSaving(false);
        onClose();
      }, 500);
    } catch (error) {
      setIsSaving(false);
    }
  };

  // Handle restoring defaults
  const handleRestoreDefaults = () => {
    const defaultPrefs: UserPreferences = {
      colorScheme: "Light",
      customThemeColor: "#3b82f6",
      fontFaceLabels: "Aptos",
      fontScaleLabels: "100%",
      fontFaceUserText: "Aptos",
      fontScaleUserText: "100%",
      textToSpeechVocalPitch: "Baritone",
      textToSpeechLocale: "US Canada English",
      textToSpeechStyle: "Precise and articulate",
      subscriberNameOptions: {
        title: true,
        firstName: true,
        middleName: true,
        lastName: true,
        suffix: true,
      },
      // Paragraph formatting preferences
      paragraphIndent: "0",
      lineSpacing: "1.15",
      paragraphSpacing: "0.5em",
    };

    setLocalPreferences(defaultPrefs);
    savePreferences(defaultPrefs, true);
  };

  // Helper functions
  const handleValueChange = (field: keyof UserPreferences, value: any) => {
    const updatedPreferences = {
      ...localPreferences,
      [field]: value
    };
    setLocalPreferences(updatedPreferences);
    savePreferences(updatedPreferences);
  };

  const handleCheckboxChange = (field: keyof UserPreferences['subscriberNameOptions'], checked: boolean) => {
    const updatedPreferences = {
      ...localPreferences,
      subscriberNameOptions: {
        ...localPreferences.subscriberNameOptions,
        [field]: checked
      }
    };
    setLocalPreferences(updatedPreferences);
    savePreferences(updatedPreferences);
  };

  const handleEmailChange = (newEmail: string) => {
    setEmail(newEmail);
    setIsEmailChanged(newEmail !== (user?.email || ''));
  };

  const handlePreviewSpeech = () => {
    speakText(sampleText);
  };

  const handleResetColor = () => {
    const updatedPreferences = {
      ...localPreferences,
      customThemeColor: "#3b82f6"
    };
    setLocalPreferences(updatedPreferences);
    savePreferences(updatedPreferences, true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose} modal={true}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">Preferences</DialogTitle>
          <DialogDescription>
            Customize your account, appearance, and document settings.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="account" className="py-4">
          <TabsList className="grid w-full grid-cols-4 mb-6">
            <TabsTrigger value="account">Account</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          {/* Account Tab */}
          <TabsContent value="account" className="space-y-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-4">Account Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      value={user?.username || ''}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">Username cannot be changed</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => handleEmailChange(e.target.value)}
                      placeholder="Enter your email address"
                    />
                    {isEmailChanged && (
                      <p className="text-xs text-blue-600">Email will be updated when you save</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                <div className="space-y-2">
                  <Label>Account Created</Label>
                  <p className="text-sm text-muted-foreground">
                    {user?.createdAt ? format(new Date(user.createdAt), 'MMMM d, yyyy') : 'Unknown'}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Account Type</Label>
                  <p className="text-sm text-muted-foreground">
                    {user?.isPremium ? 'Premium' : 'Free'}
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Appearance Tab */}
          <TabsContent value="appearance" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Visual Preferences</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Color Settings */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="colorScheme">Color Scheme</Label>
                    <Select
                      value={localPreferences.colorScheme}
                      onValueChange={(value) => handleValueChange('colorScheme', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select color scheme" />
                      </SelectTrigger>
                      <SelectContent>
                        {colorSchemeOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customThemeColor">Theme Color</Label>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-10 h-10 rounded-md border cursor-pointer"
                        style={{ backgroundColor: localPreferences.customThemeColor }}
                        onClick={() => {
                          const input = document.createElement('input');
                          input.type = 'color';
                          input.value = localPreferences.customThemeColor;
                          input.onchange = (e) => handleValueChange('customThemeColor', (e.target as HTMLInputElement).value);
                          input.click();
                        }}
                      />
                      <Input
                        value={localPreferences.customThemeColor}
                        onChange={(e) => handleValueChange('customThemeColor', e.target.value)}
                        className="flex-1 font-mono text-sm"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleResetColor}
                      >
                        Reset
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Font Settings */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="fontFaceLabels">Label Font</Label>
                    <Select
                      value={localPreferences.fontFaceLabels}
                      onValueChange={(value) => handleValueChange('fontFaceLabels', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select font" />
                      </SelectTrigger>
                      <SelectContent>
                        {fontFaceOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fontScaleLabels">Label Font Size</Label>
                    <Select
                      value={localPreferences.fontScaleLabels}
                      onValueChange={(value) => handleValueChange('fontScaleLabels', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select size" />
                      </SelectTrigger>
                      <SelectContent>
                        {fontScaleOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fontFaceUserText">Content Font</Label>
                    <Select
                      value={localPreferences.fontFaceUserText}
                      onValueChange={(value) => handleValueChange('fontFaceUserText', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select font" />
                      </SelectTrigger>
                      <SelectContent>
                        {fontFaceOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fontScaleUserText">Content Font Size</Label>
                    <Select
                      value={localPreferences.fontScaleUserText}
                      onValueChange={(value) => handleValueChange('fontScaleUserText', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select size" />
                      </SelectTrigger>
                      <SelectContent>
                        {fontScaleOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Accessibility Tab */}
          <TabsContent value="accessibility" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Accessibility Features</h3>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="textToSpeechVocalPitch">Voice Pitch</Label>
                    <Select
                      value={localPreferences.textToSpeechVocalPitch}
                      onValueChange={(value) => handleValueChange('textToSpeechVocalPitch', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select voice pitch" />
                      </SelectTrigger>
                      <SelectContent>
                        {vocalPitchOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="textToSpeechLocale">Voice Locale</Label>
                    <Select
                      value={localPreferences.textToSpeechLocale}
                      onValueChange={(value) => handleValueChange('textToSpeechLocale', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select voice locale" />
                      </SelectTrigger>
                      <SelectContent>
                        {localeOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="textToSpeechStyle">Speech Style</Label>
                    <Select
                      value={localPreferences.textToSpeechStyle}
                      onValueChange={(value) => handleValueChange('textToSpeechStyle', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select speech style" />
                      </SelectTrigger>
                      <SelectContent>
                        {speechStyleOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sampleText">Test Text-to-Speech</Label>
                    <Textarea
                      id="sampleText"
                      value={sampleText}
                      onChange={(e) => setSampleText(e.target.value)}
                      placeholder="Enter text to test speech settings"
                      rows={3}
                    />
                    <Button
                      variant="outline"
                      onClick={handlePreviewSpeech}
                      className="w-full"
                    >
                      Preview Speech
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Documents Tab */}
          <TabsContent value="documents" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Document Preferences</h3>
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Subscriber Name Options</Label>
                  <p className="text-sm text-muted-foreground mb-3">
                    Choose which name components to include in document formatting
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="title"
                        checked={localPreferences.subscriberNameOptions.title}
                        onCheckedChange={(checked) => handleCheckboxChange('title', !!checked)}
                      />
                      <Label htmlFor="title">Title (Mr., Mrs., Dr.)</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="firstName"
                        checked={localPreferences.subscriberNameOptions.firstName}
                        onCheckedChange={(checked) => handleCheckboxChange('firstName', !!checked)}
                      />
                      <Label htmlFor="firstName">First Name</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="middleName"
                        checked={localPreferences.subscriberNameOptions.middleName}
                        onCheckedChange={(checked) => handleCheckboxChange('middleName', !!checked)}
                      />
                      <Label htmlFor="middleName">Middle Name</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="lastName"
                        checked={localPreferences.subscriberNameOptions.lastName}
                        onCheckedChange={(checked) => handleCheckboxChange('lastName', !!checked)}
                      />
                      <Label htmlFor="lastName">Last Name</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="suffix"
                        checked={localPreferences.subscriberNameOptions.suffix}
                        onCheckedChange={(checked) => handleCheckboxChange('suffix', !!checked)}
                      />
                      <Label htmlFor="suffix">Suffix (Jr., Sr., III)</Label>
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t">
                  <Label className="text-base font-medium">Paragraph Formatting</Label>
                  <p className="text-sm text-muted-foreground mb-4">
                    Customize how paragraphs appear in your documents
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="paragraphIndent">First Line Indent</Label>
                      <Select
                        value={localPreferences.paragraphIndent}
                        onValueChange={(value) => handleValueChange('paragraphIndent', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select indent" />
                        </SelectTrigger>
                        <SelectContent>
                          {paragraphIndentOptions.map(option => (
                            <SelectItem key={option} value={option}>
                              {option === "0" ? "None" : option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="lineSpacing">Line Spacing</Label>
                      <Select
                        value={localPreferences.lineSpacing}
                        onValueChange={(value) => handleValueChange('lineSpacing', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select spacing" />
                        </SelectTrigger>
                        <SelectContent>
                          {lineSpacingOptions.map(option => (
                            <SelectItem key={option} value={option}>
                              {option}x
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paragraphSpacing">Paragraph Spacing</Label>
                      <Select
                        value={localPreferences.paragraphSpacing}
                        onValueChange={(value) => handleValueChange('paragraphSpacing', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select spacing" />
                        </SelectTrigger>
                        <SelectContent>
                          {paragraphSpacingOptions.map(option => (
                            <SelectItem key={option} value={option}>
                              {option === "0" ? "None" : option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleRestoreDefaults}
          >
            Restore Defaults
          </Button>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleClose(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}