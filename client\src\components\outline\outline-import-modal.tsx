import React, { useState, useRef } from 'react';
import { OutlineItem } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Upload, File, Loader2, FileText, FileSpreadsheet } from 'lucide-react';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface OutlineImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (outline: OutlineItem[]) => void;
  currentOutline?: OutlineItem[];
  isPremiumUser?: boolean;
}

/**
 * Parses text content with indentation-based hierarchy
 * Supports spaces, tabs, and mixed indentation.
 */
function parseIndentedText(text: string): OutlineItem[] {
  const lines = text.trim().split('\n');
  if (lines.length === 0) return [];

  const result: OutlineItem[] = [];
  let stackItems: { item: OutlineItem, indent: number }[] = [];
  let currentIndent = 0;
  
  // Analyze the first few lines to determine indentation style
  const indentSizes: number[] = [];
  for (let i = 0; i < Math.min(lines.length, 5); i++) {
    const leadingSpaces = lines[i].match(/^(\s*)/)?.[1].length || 0;
    if (leadingSpaces > 0) {
      indentSizes.push(leadingSpaces);
    }
  }
  
  // Find most common indentation size (or default to 2)
  const indentSize = indentSizes.length > 0 
    ? Math.min(...indentSizes.filter(s => s > 0)) 
    : 2;

  for (const line of lines) {
    if (!line.trim()) continue; // Skip empty lines
    
    // Calculate indentation level
    const leadingSpaces = line.match(/^(\s*)/)?.[1].length || 0;
    const level = Math.floor(leadingSpaces / indentSize);
    
    // Create new item
    const item: OutlineItem = {
      id: crypto.randomUUID(),
      number: '',
      title: line.trim().replace(/^[0-9.]*[\s.]*/, '').trim(), // Remove any leading numbers/bullets
      children: []
    };
    
    if (level === 0) {
      // Top-level item
      result.push(item);
      stackItems = [{ item, indent: level }];
    } else if (level > currentIndent) {
      // Child of previous item
      const parent = stackItems[stackItems.length - 1].item;
      if (!parent.children) parent.children = [];
      parent.children.push(item);
      stackItems.push({ item, indent: level });
    } else {
      // Find the appropriate parent at this level or above
      while (stackItems.length > 0 && stackItems[stackItems.length - 1].indent >= level) {
        stackItems.pop();
      }
      
      if (stackItems.length === 0) {
        // If we've popped everything, this is a top-level item
        result.push(item);
        stackItems = [{ item, indent: level }];
      } else {
        // Add to the parent at the appropriate level
        const parent = stackItems[stackItems.length - 1].item;
        if (!parent.children) parent.children = [];
        parent.children.push(item);
        stackItems.push({ item, indent: level });
      }
    }
    
    currentIndent = level;
  }
  
  return result;
}

/**
 * Detects the most likely format of the input text
 * Returns 'indented', 'bulleted', or 'unknown'
 */
function detectFormat(text: string): 'indented' | 'bulleted' | 'unknown' {
  const lines = text.trim().split('\n').filter(line => line.trim());
  if (lines.length === 0) return 'unknown';

  // Regex patterns for bullet/number detection
  const bulletPattern = /^(\s*)([*\-•○◦⁃⦾⦿]+)\s+(.+)$/;
  const numberPattern = /^(\s*)(\d+[.)]\s+|\w+[.)]\s+|[ivxIVX]+[.)]\s+)(.+)$/;

  let bulletCount = 0;
  let indentedCount = 0;
  let totalLines = 0;

  for (const line of lines) {
    totalLines++;

    // Check if line has bullet/number markers
    if (line.match(bulletPattern) || line.match(numberPattern)) {
      bulletCount++;
    }

    // Check if line has indentation (but no bullet markers)
    const leadingSpaces = line.match(/^(\s*)/)?.[1].length || 0;
    if (leadingSpaces > 0 && !line.match(bulletPattern) && !line.match(numberPattern)) {
      indentedCount++;
    }
  }

  // Calculate percentages
  const bulletPercentage = bulletCount / totalLines;
  const indentedPercentage = indentedCount / totalLines;

  // If more than 30% of lines have bullets/numbers, it's likely a bulleted list
  if (bulletPercentage > 0.3) {
    return 'bulleted';
  }

  // If more than 30% of lines have indentation (without bullets), it's likely indented
  if (indentedPercentage > 0.3) {
    return 'indented';
  }

  // If we have any bullets at all and few indented lines, prefer bulleted
  if (bulletCount > 0 && indentedCount <= bulletCount) {
    return 'bulleted';
  }

  // If we have any indented lines and few bullets, prefer indented
  if (indentedCount > 0 && bulletCount <= indentedCount) {
    return 'indented';
  }

  // Default to indented for ambiguous cases
  return 'indented';
}

/**
 * Parses text content with bullet or number-based hierarchy
 * Supports various formats like *, -, 1., 1), I., A., etc.
 */
function parseBulletedText(text: string): OutlineItem[] {
  const lines = text.trim().split('\n');
  if (lines.length === 0) return [];

  const result: OutlineItem[] = [];
  let stackItems: { item: OutlineItem, level: number }[] = [];
  
  // Regex patterns for different types of list markers
  const bulletPattern = /^(\s*)([*\-•○◦⁃⦾⦿]+)\s+(.+)$/;
  const numberPattern = /^(\s*)(\d+[.)]\s+|\w+[.)]\s+|[ivxIVX]+[.)]\s+)(.+)$/;
  
  for (const line of lines) {
    if (!line.trim()) continue; // Skip empty lines
    
    // Try to match bullet points or numbered lists
    const bulletMatch = line.match(bulletPattern);
    const numberMatch = line.match(numberPattern);
    const match = bulletMatch || numberMatch;
    
    if (!match) {
      // If no bullet/number found, treat as plain text at current level
      const item: OutlineItem = {
        id: crypto.randomUUID(),
        number: '',
        title: line.trim(),
        children: []
      };
      
      if (stackItems.length === 0) {
        result.push(item);
      } else {
        const parent = stackItems[stackItems.length - 1].item;
        if (!parent.children) parent.children = [];
        parent.children.push(item);
      }
      continue;
    }
    
    const [, indent, marker, content] = match;
    const level = indent.length;
    
    // Create new item
    const item: OutlineItem = {
      id: crypto.randomUUID(),
      number: '',
      title: content.trim(),
      children: []
    };
    
    if (stackItems.length === 0) {
      // First item
      result.push(item);
      stackItems.push({ item, level });
    } else if (level > stackItems[stackItems.length - 1].level) {
      // Child of previous item
      const parent = stackItems[stackItems.length - 1].item;
      if (!parent.children) parent.children = [];
      parent.children.push(item);
      stackItems.push({ item, level });
    } else {
      // Pop stack until we find a parent with lower level
      while (stackItems.length > 0 && stackItems[stackItems.length - 1].level >= level) {
        stackItems.pop();
      }
      
      if (stackItems.length === 0) {
        // If we've popped everything, this is a top-level item
        result.push(item);
      } else {
        // Add to the parent at the appropriate level
        const parent = stackItems[stackItems.length - 1].item;
        if (!parent.children) parent.children = [];
        parent.children.push(item);
      }
      
      stackItems.push({ item, level });
    }
  }
  
  return result;
}

export function OutlineImportModal({ isOpen, onClose, onImport, currentOutline = [], isPremiumUser = false }: OutlineImportModalProps) {
  const [text, setText] = useState('');
  const [format, setFormat] = useState('auto');
  const [previewMode, setPreviewMode] = useState(false);
  const [parsedOutline, setParsedOutline] = useState<OutlineItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Helper function to check if the current outline has meaningful content
  const hasExistingContent = () => {
    if (!currentOutline || currentOutline.length === 0) return false;

    // Check if there's more than just a default "Introduction" section
    if (currentOutline.length === 1 &&
        currentOutline[0].title.toLowerCase().includes('introduction') &&
        (!currentOutline[0].children || currentOutline[0].children.length === 0)) {
      return false;
    }

    return true;
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
    setPreviewMode(false);
  };
  
  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };
  
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setPreviewMode(false);

    try {
      let content = '';
      let fileTypeDescription = '';

      // Handle different file types
      if (file.type === 'text/plain' ||
          file.name.endsWith('.txt') ||
          file.name.endsWith('.md') ||
          file.type === 'text/markdown') {
        // Plain text files
        content = await file.text();
        fileTypeDescription = file.name.endsWith('.md') ? 'Markdown file' : 'text file';

      } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                file.name.endsWith('.docx')) {
        // Word documents - extract plain text using mammoth
        try {
          const arrayBuffer = await file.arrayBuffer();
          const result = await mammoth.extractRawText({ arrayBuffer });
          content = result.value;
          fileTypeDescription = 'Word document';

          if (result.messages.length > 0) {
            console.log('Mammoth warnings:', result.messages);
          }
        } catch (error) {
          console.error('Error processing Word document:', error);
          toast({
            title: 'Error',
            description: 'Failed to extract text from the Word document. Please try a different file.',
            variant: 'destructive',
          });
          return;
        }
      } else if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.name.endsWith('.xlsx')) {
        // Excel documents - improved handling for outline structure
        try {
          const arrayBuffer = await file.arrayBuffer();
          const workbook = XLSX.read(arrayBuffer, { type: 'array' });

          // Get first worksheet
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];

          // Convert to JSON with better structure preservation
          const data = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' });

          // Convert data to outline-friendly text format
          const textContent = data
            .map((row: any) => {
              if (!Array.isArray(row) || row.every(cell => !cell)) return ''; // Skip empty rows

              // Find the first non-empty cell to determine indentation level
              let indentLevel = 0;
              for (let i = 0; i < row.length; i++) {
                if (row[i]) {
                  indentLevel = i;
                  break;
                }
              }

              // Create indentation and get the content
              const indent = '  '.repeat(indentLevel);
              const content = row.find(cell => cell) || '';

              return indent + content;
            })
            .filter(line => line.trim()) // Remove empty lines
            .join('\n');

          content = textContent;
          fileTypeDescription = 'Excel spreadsheet';
        } catch (error) {
          console.error('Error processing Excel file:', error);
          toast({
            title: 'Error',
            description: 'Failed to extract data from the Excel file. Please try a different file.',
            variant: 'destructive',
          });
          return;
        }
      } else {
        // Try to read as text anyway
        try {
          content = await file.text();
          fileTypeDescription = 'file (unrecognized format)';
        } catch (error) {
          toast({
            title: 'Error',
            description: 'Could not read file contents. Please try another file format.',
            variant: 'destructive',
          });
          return;
        }
      }

      // Set the content
      setText(content);

      // Automatically generate preview after loading file
      setTimeout(() => {
        generatePreview();
      }, 100);

      toast({
        title: 'File loaded successfully',
        description: `Imported text from ${fileTypeDescription} "${file.name}". Preview generated automatically.`,
      });

    } catch (error) {
      console.error('Error reading file:', error);
      toast({
        title: 'Error',
        description: 'Failed to read file. Please try a different file or format.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  
  const generatePreview = () => {
    try {
      let outline: OutlineItem[] = [];

      if (format === 'auto') {
        // Use auto-detection to determine the best format
        const detectedFormat = detectFormat(text);

        if (detectedFormat === 'bulleted') {
          outline = parseBulletedText(text);
        } else if (detectedFormat === 'indented') {
          outline = parseIndentedText(text);
        } else {
          // If format is unknown, try both and pick the one with better results
          const indentedResult = parseIndentedText(text);
          const bulletedResult = parseBulletedText(text);

          // Choose the result with more items, or indented as fallback
          if (bulletedResult.length > indentedResult.length) {
            outline = bulletedResult;
          } else {
            outline = indentedResult;
          }
        }
      } else if (format === 'indented') {
        outline = parseIndentedText(text);
      } else if (format === 'bulleted') {
        outline = parseBulletedText(text);
      }

      setParsedOutline(outline);
      setPreviewMode(true);

      if (outline.length === 0) {
        toast({
          title: 'Warning',
          description: 'Could not parse any outline items from the text. Please check your format.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error parsing outline:', error);
      toast({
        title: 'Error',
        description: 'Failed to parse outline. Please check your format.',
        variant: 'destructive',
      });
    }
  };
  
  const handleImport = () => {
    if (parsedOutline.length === 0) {
      toast({
        title: 'Warning',
        description: 'No outline items to import. Please check your text or format.',
        variant: 'destructive',
      });
      return;
    }

    // Check if there's existing content that would be replaced
    if (hasExistingContent()) {
      setShowConfirmation(true);
      return;
    }

    // Proceed with import if no existing content
    performImport();
  };

  const performImport = () => {
    onImport(parsedOutline);
    toast({
      title: 'Success',
      description: `Imported ${parsedOutline.length} top-level outline items.`,
    });
    setShowConfirmation(false);
    onClose();
  };

  const handleConfirmImport = () => {
    performImport();
  };

  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
  };
  
  // Recursive function to render outline items for preview
  const renderOutlinePreview = (items: OutlineItem[], level = 0) => {
    return (
      <ul className={`${level > 0 ? 'ml-4' : ''} pl-0 list-inside`}>
        {items.map((item) => (
          <li key={item.id} className="my-1">
            <div className="flex items-center">
              <span className={`font-medium ${level === 0 ? 'text-primary' : ''}`}>
                {item.title}
              </span>
            </div>
            {item.children && item.children.length > 0 && (
              renderOutlinePreview(item.children, level + 1)
            )}
          </li>
        ))}
      </ul>
    );
  };
  
  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Import Outline</DialogTitle>
            <DialogDescription>
              Import your outline from a file or paste text below. We'll automatically detect the format and convert it to the application structure.
            </DialogDescription>
          </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm">Format:</span>
              <Select
                value={format}
                onValueChange={(value) => {
                  setFormat(value);
                  setPreviewMode(false);
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Auto-detect format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto-detect</SelectItem>
                  <SelectItem value="indented">Indented text</SelectItem>
                  <SelectItem value="bulleted">Bulleted/numbered list</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {!previewMode && (
              <Button 
                variant="outline" 
                onClick={generatePreview}
                disabled={!text.trim()}
              >
                Preview
              </Button>
            )}
          </div>
          
          <Tabs defaultValue="input" className="flex-1 flex flex-col overflow-hidden">
            <TabsList>
              <TabsTrigger value="input">Input</TabsTrigger>
              <TabsTrigger 
                value="preview" 
                disabled={!previewMode || parsedOutline.length === 0}
              >
                Preview
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="input" className="flex-1 overflow-hidden flex flex-col">
              <div className="space-y-3 mb-4">
                <div className="text-center">
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    className="flex items-center gap-2"
                    onClick={handleFileSelect}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    Import from file
                  </Button>
                  <p className="text-xs text-muted-foreground mt-2">
                    Automatically detects format and generates preview
                  </p>
                </div>

                <div className="flex flex-wrap items-center justify-center gap-2">
                  <div className="flex items-center gap-1 text-xs text-muted-foreground px-2 py-1 rounded-md bg-green-50 border border-green-200">
                    <File className="h-3 w-3 text-green-600" />
                    <span className="text-green-700">.txt, .md</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground px-2 py-1 rounded-md bg-blue-50 border border-blue-200">
                    <FileText className="h-3 w-3 text-blue-600" />
                    <span className="text-blue-700">.docx (Word)</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground px-2 py-1 rounded-md bg-orange-50 border border-orange-200">
                    <FileSpreadsheet className="h-3 w-3 text-orange-600" />
                    <span className="text-orange-700">.xlsx (Excel)</span>
                  </div>
                </div>

                <div className="text-center text-sm text-muted-foreground">
                  <span>or paste your outline below</span>
                </div>

                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".txt,.md,.docx,.xlsx,text/plain,text/markdown,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                />
              </div>
              <Textarea
                value={text}
                onChange={handleTextChange}
                placeholder="Paste your outline here. Examples:

📝 Indented text:
Introduction
  Background
  Problem Statement
Methods
  Data Collection
  Analysis

🔸 Bulleted lists:
* Introduction
  * Background
  * Problem Statement
* Methods
  * Data Collection
  * Analysis

🔢 Numbered lists:
1. Introduction
   1.1 Background
   1.2 Problem Statement
2. Methods
   2.1 Data Collection
   2.2 Analysis"
                className="w-full flex-1 resize-none min-h-[260px] font-mono text-sm"
              />
            </TabsContent>
            
            <TabsContent value="preview" className="flex-1 overflow-auto border rounded-md p-4">
              {previewMode && parsedOutline.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 px-3 py-2 rounded-md border border-green-200">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Successfully parsed {parsedOutline.length} top-level outline item{parsedOutline.length !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="outline-preview">
                    {renderOutlinePreview(parsedOutline)}
                  </div>
                </div>
              ) : previewMode && parsedOutline.length === 0 ? (
                <div className="text-center py-8 space-y-4">
                  <div className="text-amber-600 bg-amber-50 px-4 py-3 rounded-md border border-amber-200">
                    <div className="font-medium">No outline items detected</div>
                    <div className="text-sm mt-1">
                      The text couldn't be parsed into an outline structure. Try:
                    </div>
                    <ul className="text-sm mt-2 space-y-1 text-left">
                      <li>• Adding bullet points (* or -) to your items</li>
                      <li>• Using consistent indentation (spaces or tabs)</li>
                      <li>• Adding numbered lists (1., 2., etc.)</li>
                      <li>• Checking the examples in the input area</li>
                    </ul>
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8 space-y-4">
                  <div className="text-lg">📋</div>
                  <div>
                    <div className="font-medium">Preview not available</div>
                    <div className="text-sm mt-1">
                      Add some text or import a file, then click "Preview" to see how your outline will look.
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleImport}
            disabled={!previewMode || parsedOutline.length === 0}
          >
            Import Outline
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Confirmation Modal for Existing Content */}
    <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-amber-600">
            <div className="w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center">
              <span className="text-amber-600 font-bold text-sm">!</span>
            </div>
            Replace Existing Outline?
          </DialogTitle>
          <DialogDescription className="space-y-3 pt-2">
            <p>
              Your document currently has <strong>{currentOutline.length} outline section{currentOutline.length !== 1 ? 's' : ''}</strong> with content.
            </p>
            <p>
              Importing this new outline will <strong>completely replace</strong> your existing outline structure.
              {isPremiumUser ? (
                <span> You can revert this change using the History feature if needed.</span>
              ) : (
                <span> This action cannot be undone without a Premium subscription.</span>
              )}
            </p>
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mt-3">
              <p className="text-sm text-amber-800">
                <strong>💡 Tip:</strong> Consider exporting your current document first as a backup before proceeding.
                {!isPremiumUser && (
                  <span> Premium users can use the History feature to revert changes.</span>
                )}
              </p>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancelConfirmation}>
            Cancel Import
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirmImport}
            className="bg-amber-600 hover:bg-amber-700"
          >
            Replace Outline
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    </>
  );
}