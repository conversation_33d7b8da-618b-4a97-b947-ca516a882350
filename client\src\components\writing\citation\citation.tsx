import React from 'react';
import { Citation } from '@/lib/types';

interface CitationComponentProps {
  citation: Citation;
  onUpdate: (citation: Citation) => void;
  onDelete: (citationId: string) => void;
  isDraggable?: boolean;
}

export function CitationComponent({ citation, onUpdate, onDelete, isDraggable = false }: CitationComponentProps) {
  // Editing functionality removed - should be done in Reference Manager

  const handleDragStart = (e: React.DragEvent) => {
    if (!isDraggable) return;
    
    // Set data for the citation being dragged
    e.dataTransfer.setData('text/plain', citation.marker); 
    e.dataTransfer.setData('application/json', JSON.stringify(citation));
    
    // Check if dark mode is active
    const isDarkMode = document.documentElement.classList.contains('dark');
    
    // Create a custom drag image with appropriate colors
    const dragIcon = document.createElement('div');
    dragIcon.className = isDarkMode 
      ? 'bg-yellow-800 text-yellow-200 text-xs rounded-full h-5 w-5 flex items-center justify-center'
      : 'bg-yellow-200 text-yellow-800 text-xs rounded-full h-5 w-5 flex items-center justify-center';
    dragIcon.textContent = citation.marker;
    dragIcon.style.position = 'absolute';
    dragIcon.style.top = '-1000px';
    document.body.appendChild(dragIcon);
    
    e.dataTransfer.setDragImage(dragIcon, 12, 12);
    
    // Set timeout to remove the element
    setTimeout(() => {
      document.body.removeChild(dragIcon);
    }, 0);
  };

  return (
    <div 
      className={`p-2 rounded-md bg-neutral-50 dark:bg-gray-800 border border-neutral-200 dark:border-gray-700 
        ${isDraggable ? 'cursor-grab active:cursor-grabbing' : ''}`}
      draggable={isDraggable}
      onDragStart={handleDragStart}
    >
      <div className="flex justify-between items-start">
        <span className="bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 
          text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {citation.marker}
        </span>
        <div className="flex-1 ml-2">
          <div>
            {/* Enhanced citation display with proper data */}
            {citation.title ? (
              <>
                <p className="text-xs font-medium text-neutral-800 dark:text-gray-100">
                  {citation.title}
                </p>
                <p className="text-xs text-neutral-600 dark:text-gray-300 mt-0.5">
                  {citation.authors && citation.authors.length > 0
                    ? citation.authors.join(', ')
                    : citation.reference.split('.')[0]
                  }
                  {citation.year && `, ${citation.year}`}
                  {citation.publisher && `, ${citation.publisher}`}
                </p>
              </>
            ) : (
              <>
                {/* If no title but has detailed parts, show structured format */}
                {citation.authors && citation.authors.length > 0 ? (
                  <>
                    <p className="text-xs font-medium text-neutral-800 dark:text-gray-100">
                      {citation.authors.join(', ')}
                      {citation.year && ` (${citation.year})`}
                    </p>
                    <p className="text-xs text-neutral-600 dark:text-gray-300 mt-0.5">
                      {citation.reference}
                    </p>
                  </>
                ) : (
                  // Fallback to just showing the reference text
                  <p className="text-xs text-neutral-700 dark:text-gray-200">{citation.reference}</p>
                )}
              </>
            )}
          </div>
        </div>
        {/* Icons removed - reference management should be done in Reference Manager */}
      </div>
    </div>
  );
}
