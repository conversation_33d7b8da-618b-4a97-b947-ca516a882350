import React, { useRef, useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Note, OutlineItem, Citation, ReferenceCollection } from "@/lib/types";
import { Minimap } from "@/components/ui/minimap";
import { ConfirmModal } from "@/components/ui/confirm-modal";
import { Editor } from "@/components/ui/editor";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { AiEnhancedRichTextEditor } from "@/components/ui/ai-enhanced-rich-text-editor";
import { CitationComponent } from "@/components/writing/citation";
import { ExportButton } from "@/components/writing/export-button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  useReferenceCollections,
  useUserReferences,
} from "@/hooks/use-references";
import { nanoid } from "nanoid";

interface WritingPanelProps {
  outline?: OutlineItem[];
  activeOutlineItemId: string | null;
  notes: Note[];
  content: string;
  citations: Citation[];
  documentId?: number;
  notesType?: "footnotes" | "endnotes";
  onContentChange: (content: string) => void;
  onAddCitation?: (referenceData?: Partial<Citation>) => any; // Updated to accept citation data
  onUpdateCitation: (citation: Citation) => void;
  onDeleteCitation: (citationId: string) => void;
  onCheckText?: () => void;
  onSpellCheck?: () => void;
  onGrammarCheck?: () => void;
  onExport?: () => void;
  onOutlineItemSelect: (itemId: string) => void;
  onShowSubscriptionModal?: () => void;
  onNotesTypeChange?: (type: "footnotes" | "endnotes") => void;
  // Note-related props for WYSIWYG editor
  onNoteCreate?: (linkedOutlineId: string, options?: { type?: 'text' | 'image' | 'video' | 'file' }) => Note;
  onNoteUpdate?: (note: Note, forceSaveNow?: boolean) => void;
  onNoteDelete?: (noteId: string) => void;
  onFileUpload?: (file: File) => Promise<string>;
  popOutPanel: () => void;
  isPoppedOut?: boolean;
  getCurrentWriting?: () => { content: string; citations: Citation[] };
  currentUserPermissionLevel?: 'view' | 'edit' | null;
  isDocumentOwner?: boolean;
}

export function WritingPanel({
  outline = [],
  activeOutlineItemId,
  notes,
  content = '',
  citations = [],
  documentId,
  notesType: externalNotesType,
  onContentChange,
  onAddCitation,
  onUpdateCitation,
  onDeleteCitation,
  onCheckText,
  onSpellCheck,
  onGrammarCheck,
  onExport,
  onOutlineItemSelect,
  onShowSubscriptionModal,
  onNotesTypeChange,
  onNoteCreate,
  onNoteUpdate,
  onNoteDelete,
  onFileUpload,
  popOutPanel,
  isPoppedOut,
  getCurrentWriting,
  currentUserPermissionLevel,
  isDocumentOwner,
}: WritingPanelProps) {
  const [activeView, setActiveView] = useState<"writing" | "notes">("writing");
  const [filteredNotes, setFilteredNotes] = useState<Note[]>([]);
  const [internalNotesType, setInternalNotesType] = useState<
    "footnotes" | "endnotes"
  >(externalNotesType || "footnotes");

  // Determine if the current user can add citations
  // Document owners and users with 'edit' permission can add citations
  // Only 'view' permission users (read-only guests) cannot add citations
  const canAddCitations = currentUserPermissionLevel !== 'view';
  const isReadOnlyGuest = currentUserPermissionLevel === 'view';
  const isEditGuest = currentUserPermissionLevel === 'edit' && !isDocumentOwner;
  const [selectedCollectionId, setSelectedCollectionId] =
    useState<string>("all");
  const [filteredCitations, setFilteredCitations] =
    useState<Citation[]>(citations);
  const editorRef = useRef<any>(null);

  // State for citation deletion confirmation modal
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [citationToDelete, setCitationToDelete] = useState<Citation | null>(null);

  // Fetch reference collections and references
  const { data: collectionsData, isLoading: isLoadingCollections } =
    useReferenceCollections();
  const { data: referencesData, isLoading: isLoadingReferences } =
    useUserReferences();

  const collections = (collectionsData as ReferenceCollection[]) || [];
  const allUserReferences = (referencesData as Citation[]) || [];

  // Create base collections list
  const baseCollections: ReferenceCollection[] = [
    {
      id: "all",
      name: "All References",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: "uncategorized",
      name: "Uncategorized",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  // Combine base and user collections
  const allCollections = [...baseCollections, ...collections];

  // References to display based on selected collection
  const [displayReferences, setDisplayReferences] = useState<Citation[]>([]);

  // Use either the externally controlled notesType or internal state
  const notesType = externalNotesType || internalNotesType;

  // State for citation highlighting
  const [highlightedCitationId, setHighlightedCitationId] = useState<string | null>(null);

  // Function to find citation text in the editor content
  const findCitationInContent = (citation: Citation): { text: string; position: number } | null => {
    if (notesType === "footnotes") {
      // For footnotes, look for superscript markers
      const supPattern = new RegExp(`<sup>\\s*\\[?\\s*${citation.marker}\\s*\\]?\\s*</sup>`, 'gi');
      const match = supPattern.exec(content);
      if (match) {
        return {
          text: match[0],
          position: match.index
        };
      }
    } else {
      // For endnotes, look for parenthetical citations
      // Try to match (Author, Year) format
      if (citation.authors && citation.authors.length > 0 && citation.year) {
        const authorLastName = citation.authors[0].split(" ").pop() || "";
        const endnotePattern = new RegExp(`\\(\\s*${authorLastName}\\s*,\\s*${citation.year}\\s*\\)`, 'gi');
        const match = endnotePattern.exec(content);
        if (match) {
          return {
            text: match[0],
            position: match.index
          };
        }
      }

      // Fallback: look for any parenthetical that contains the citation marker
      const markerPattern = new RegExp(`\\([^)]*${citation.marker}[^)]*\\)`, 'gi');
      const match = markerPattern.exec(content);
      if (match) {
        return {
          text: match[0],
          position: match.index
        };
      }
    }

    return null;
  };

  // Function to highlight citation in editor
  const highlightCitationInEditor = (citation: Citation) => {
    console.log('🎯 NEW VERSION - Highlighting citation:', citation.marker);
    const citationInfo = findCitationInContent(citation);
    if (!citationInfo) {
      console.log('🎯 No citation info found for:', citation.marker);
      return;
    }

    // Get the actual contenteditable element from the AiEnhancedRichTextEditor
    const editorElement = document.querySelector('#editor [contenteditable="true"]') as HTMLElement;
    console.log('🎯 Editor element found:', !!editorElement);
    if (!editorElement) {
      console.log('🎯 No contenteditable element found in #editor');
      return;
    }

    // Simple highlighting implementation - add a temporary background color
    try {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();

        // Find and highlight ALL instances of the citation text
        const walker = document.createTreeWalker(
          editorElement,
          NodeFilter.SHOW_TEXT,
          null
        );

        // First, collect all text nodes and their citation positions with sentence boundaries
        const citationMatches: Array<{node: Text, sentenceStart: number, citationStart: number, citationEnd: number}> = [];
        let textNode;

        // For footnotes, we need to look for actual superscript elements, not just text
        if (notesType === "footnotes") {
          // Find all superscript elements that contain the citation marker
          const supElements = editorElement.querySelectorAll('sup');
          console.log(`🎯 Found ${supElements.length} superscript elements in editor`);

          supElements.forEach((supElement) => {
            const supText = supElement.textContent?.trim();
            if (supText === citation.marker) {
              console.log(`🎯 Found matching footnote superscript with marker "${citation.marker}"`);

              // Find the text node that contains the sentence before this superscript
              const parentElement = supElement.parentElement;
              if (parentElement) {
                // Get all text nodes in the parent element
                const walker = document.createTreeWalker(
                  parentElement,
                  NodeFilter.SHOW_TEXT,
                  null
                );

                let textNode;
                while (textNode = walker.nextNode()) {
                  // Skip the text node inside the superscript itself
                  if (supElement.contains(textNode)) continue;

                  const text = textNode.textContent || '';
                  if (text.trim().length > 0) {
                    // Find sentence boundaries in this text node
                    let sentenceStart = 0;
                    const textLength = text.length;

                    // Look for the last sentence boundary before the end of this text
                    for (let i = textLength - 1; i >= 0; i--) {
                      const char = text[i];
                      if (char === '.' || char === '!' || char === '?') {
                        sentenceStart = i + 1;
                        while (sentenceStart < textLength && /\s/.test(text[sentenceStart])) {
                          sentenceStart++;
                        }
                        break;
                      }
                    }

                    console.log(`🎯 Found text node for footnote sentence: "${text.substring(sentenceStart)}"`);

                    citationMatches.push({
                      node: textNode as Text,
                      sentenceStart: sentenceStart,
                      citationStart: textLength, // Highlight to end of text node
                      citationEnd: textLength
                    });
                    break; // Only take the first relevant text node
                  }
                }
              }
            }
          });
        } else {
          // For endnotes, find all occurrences in text nodes
          while (textNode = walker.nextNode()) {
            const text = textNode.textContent || '';
            let searchIndex = 0;

            while (true) {
              const index = text.indexOf(citationInfo.text, searchIndex);
              if (index === -1) break;

              console.log(`🎯 Found endnote citation at index ${index} in text node`);

              // Find the start of the sentence before the citation
              // Look for sentence boundaries: ., !, ?, or start of text
              let sentenceStart = 0;
              for (let i = index - 1; i >= 0; i--) {
                const char = text[i];
                if (char === '.' || char === '!' || char === '?') {
                  // Found sentence boundary, start after the punctuation and any whitespace
                  sentenceStart = i + 1;
                  while (sentenceStart < index && /\s/.test(text[sentenceStart])) {
                    sentenceStart++;
                  }
                  break;
                }
              }

              console.log(`🎯 Endnote sentence starts at index ${sentenceStart}, citation at ${index}`);

              citationMatches.push({
                node: textNode as Text,
                sentenceStart: sentenceStart,
                citationStart: index,
                citationEnd: index + citationInfo.text.length
              });

              searchIndex = index + citationInfo.text.length;
            }
          }
        }

        console.log(`🎯 Found ${citationMatches.length} total citation instances to highlight`);

        // Now highlight all matches in reverse order to avoid DOM structure issues
        const highlightSpans: HTMLElement[] = [];
        for (let i = citationMatches.length - 1; i >= 0; i--) {
          const match = citationMatches[i];

          try {
            const range = document.createRange();
            // Highlight from the sentence start to the end of the citation
            range.setStart(match.node, match.sentenceStart);
            range.setEnd(match.node, match.citationEnd);

            // Add highlight
            const span = document.createElement('span');
            span.style.backgroundColor = 'yellow';
            span.style.transition = 'background-color 0.3s ease';
            span.style.padding = '2px';
            span.style.borderRadius = '3px';

            range.surroundContents(span);
            highlightSpans.push(span);
            console.log(`🎯 Successfully highlighted sentence with citation instance ${i + 1}`);
          } catch (error) {
            console.error('Error highlighting citation:', error);
          }
        }

        console.log(`🎯 Highlighted ${highlightSpans.length} instances of citation`);

        // Remove all highlights after 2 seconds
        setTimeout(() => {
          highlightSpans.forEach(span => {
            if (span.parentNode) {
              const parent = span.parentNode;
              // Move the text content out of the highlight span
              while (span.firstChild) {
                parent.insertBefore(span.firstChild, span);
              }
              parent.removeChild(span);
              parent.normalize();
            }
          });
        }, 2000);
      }
    } catch (error) {
      console.log('🎯 Error highlighting citation:', error);
    }
  };

  // Function to remove citation highlighting
  const removeHighlighting = () => {
    // Get the editor element directly from the DOM
    const editorElement = document.querySelector('#editor [contenteditable="true"]') as HTMLElement;
    if (!editorElement) return;

    // Remove all citation highlights
    const highlights = editorElement.querySelectorAll('span[style*="background-color: yellow"]');
    highlights.forEach((highlight: Element) => {
      const parent = highlight.parentNode;
      if (parent) {
        // Move the text content out of the highlight span
        while (highlight.firstChild) {
          parent.insertBefore(highlight.firstChild, highlight);
        }
        parent.removeChild(highlight);
        parent.normalize();
      }
    });
  };

  // Function to remove all instances of a citation from the text
  const removeCitationFromText = (citation: Citation) => {
    const editorElement = document.querySelector('#editor [contenteditable="true"]') as HTMLElement;
    if (!editorElement) {
      console.error('Editor element not found');
      return false;
    }

    console.log(`🗑️ Removing all instances of citation: ${citation.marker}`);
    console.log(`🗑️ Content before removal:`, editorElement.innerHTML.substring(0, 200) + '...');
    let removedCount = 0;

    if (notesType === "footnotes") {
      // For footnotes, remove superscript elements with matching marker
      const supElements = editorElement.querySelectorAll('sup');
      supElements.forEach((supElement) => {
        const supText = supElement.textContent?.trim();
        if (supText === citation.marker) {
          console.log(`🗑️ Removing footnote superscript: ${supText}`);
          supElement.remove();
          removedCount++;
        }
      });
    } else {
      // For endnotes, remove parenthetical citations
      const citationInfo = findCitationInContent(citation);
      if (citationInfo) {
        const walker = document.createTreeWalker(
          editorElement,
          NodeFilter.SHOW_TEXT,
          null
        );

        const textNodesToModify: Array<{node: Text, replacements: Array<{start: number, end: number}>}> = [];
        let textNode;

        // Find all text nodes containing the citation
        while (textNode = walker.nextNode()) {
          const text = textNode.textContent || '';
          const replacements: Array<{start: number, end: number}> = [];
          let searchIndex = 0;

          // Find all occurrences in this text node
          while (true) {
            const index = text.indexOf(citationInfo.text, searchIndex);
            if (index === -1) break;

            console.log(`🗑️ Found endnote citation to remove at index ${index}`);
            replacements.push({
              start: index,
              end: index + citationInfo.text.length
            });
            removedCount++;
            searchIndex = index + citationInfo.text.length;
          }

          if (replacements.length > 0) {
            textNodesToModify.push({node: textNode as Text, replacements});
          }
        }

        // Remove citations from text nodes (in reverse order to maintain indices)
        textNodesToModify.forEach(({node, replacements}) => {
          let text = node.textContent || '';
          // Process replacements in reverse order to maintain indices
          for (let i = replacements.length - 1; i >= 0; i--) {
            const {start, end} = replacements[i];
            text = text.substring(0, start) + text.substring(end);
          }
          node.textContent = text;
        });
      }
    }

    console.log(`🗑️ Removed ${removedCount} instances of citation`);
    console.log(`🗑️ Content after removal:`, editorElement.innerHTML.substring(0, 200) + '...');

    // Always trigger content change to save the updated content and refresh the sidebar
    // This ensures the sidebar re-analyzes the content and updates its citation list
    onContentChange(editorElement.innerHTML);

    return removedCount > 0;
  };

  // Function to handle citation deletion with confirmation
  const handleDeleteCitation = (citation: Citation) => {
    console.log(`🗑️ Opening deletion confirmation modal for citation: ${citation.marker}`);
    setCitationToDelete(citation);
    setDeleteModalOpen(true);
  };

  // Function to confirm citation deletion
  const confirmDeleteCitation = () => {
    if (!citationToDelete) return;

    console.log(`🗑️ User confirmed deletion of citation: ${citationToDelete.marker}`);
    console.log(`🗑️ Citation ID: ${citationToDelete.id}`);
    console.log(`🗑️ Citation object:`, citationToDelete);

    // Remove from text content - this will automatically trigger content change and sidebar refresh
    const removedFromText = removeCitationFromText(citationToDelete);

    if (removedFromText) {
      console.log(`🗑️ Successfully removed citation from text, sidebar should refresh automatically`);
    } else {
      console.log(`🗑️ No instances found in text, but sidebar should still refresh`);
    }

    // Close modal and reset state
    setDeleteModalOpen(false);
    setCitationToDelete(null);
  };

  // Function to cancel citation deletion
  const cancelDeleteCitation = () => {
    console.log(`🗑️ User cancelled deletion of citation: ${citationToDelete?.marker}`);
    setDeleteModalOpen(false);
    setCitationToDelete(null);
  };

  // Function to scroll to citation in editor
  const scrollToCitation = (citation: Citation) => {
    console.log('scrollToCitation called for citation:', citation);
    const citationInfo = findCitationInContent(citation);
    console.log('Citation info found for scroll:', citationInfo);
    if (!citationInfo) return;

    // Get the editor element directly from the DOM
    const editorElement = document.querySelector('#editor [contenteditable="true"]') as HTMLElement;
    console.log('Editor element found for scroll:', editorElement);
    if (!editorElement) return;

    // Highlight the citation first
    highlightCitationInEditor(citation);

    // Calculate approximate scroll position based on content position
    const totalContentLength = content.length;
    const citationPosition = citationInfo.position;
    const scrollRatio = citationPosition / totalContentLength;

    // Get editor dimensions
    const editorHeight = editorElement.scrollHeight;
    const viewportHeight = editorElement.clientHeight;
    const maxScroll = editorHeight - viewportHeight;

    // Calculate target scroll position
    const targetScroll = scrollRatio * maxScroll;

    // Scroll to the citation with smooth animation
    editorElement.scrollTo({
      top: Math.max(0, Math.min(targetScroll, maxScroll)),
      behavior: 'smooth'
    });
  };

  // Insert citation into editor by creating an element and appending it to the content
  const insertCitationIntoEditor = (citation: Citation) => {
    if (!activeOutlineItemId) return;

    // First, add the citation to the document's citations array using onAddCitation
    if (onAddCitation) {
      // This ensures the citation is properly stored with all its metadata
      console.log("Adding citation to document:", citation);
      // Pass the citation data to create a proper citation record in the document
      const newCitation = onAddCitation(citation);
      console.log("New citation created:", newCitation);
    }

    // We need to work with DOM elements to properly format citations
    // Create a temporary container
    const tempContainer = document.createElement("div");
    tempContainer.innerHTML = content;

    if (notesType === "footnotes") {
      // Create an actual superscript element
      const supElement = document.createElement("sup");
      supElement.textContent = citation.marker;
      tempContainer.appendChild(supElement);

      console.log("Inserting footnote citation as DOM element");
    } else {
      // Format as endnote (parenthetical citation)
      // Extract author or use marker if no clear author available
      let authorText = "Citation";
      if (citation.authors && citation.authors.length > 0) {
        authorText = citation.authors[0].split(" ").pop() || citation.marker;
      } else if (citation.reference) {
        const parts = citation.reference.split(".");
        authorText = parts[0] || citation.marker;
      } else {
        authorText = citation.marker;
      }

      // Use citation year or current year
      const yearText = citation.year || new Date().getFullYear().toString();

      // Create text node with the formatted citation
      const endnoteText = document.createTextNode(
        `(${authorText}, ${yearText})`,
      );
      tempContainer.appendChild(endnoteText);

      console.log("Inserting endnote citation as text node");
    }

    // Get the updated content and update the editor
    const newContent = tempContainer.innerHTML;
    onContentChange(newContent);
  };

  // Function to convert all citations in the document when switching modes
  const convertAllCitations = (
    contentToConvert: string,
    fromType: "footnotes" | "endnotes",
    toType: "footnotes" | "endnotes",
  ): string => {
    let updatedContent = contentToConvert;

    console.log(`Converting citations from ${fromType} to ${toType}`);

    // Use DOM manipulation to convert footnotes to endnotes and vice versa
    // This approach avoids issues with HTML markup and keeps proper formatting

    // Create a temporary container to work with the content
    const tempContainer = document.createElement("div");
    tempContainer.innerHTML = contentToConvert;

    if (fromType === "footnotes" && toType === "endnotes") {
      // Convert from footnotes to endnotes
      // Find all superscript elements (footnotes) in the content
      const supElements = Array.from(tempContainer.querySelectorAll("sup"));

      console.log(
        `Found ${supElements.length} superscript elements to convert to endnotes`,
      );

      // Process each superscript element
      for (const supElement of supElements) {
        const marker = supElement.textContent?.trim();

        if (marker && /^\d+$/.test(marker)) {
          // Find the citation with this marker
          console.log(`Looking for citation with marker: "${marker}" (type: ${typeof marker})`);
          console.log("Available citations from props:", citations.map(c => ({ id: c.id, marker: c.marker, markerType: typeof c.marker })));
          console.log("Available filtered citations:", filteredCitations.map(c => ({ id: c.id, marker: c.marker, markerType: typeof c.marker })));

          // Try to find citation in filtered citations first (these are the ones actually in the content)
          // then fall back to the citations prop
          const citation = filteredCitations.find((c) => c.marker === marker || c.marker === parseInt(marker) || c.marker.toString() === marker) ||
                           citations.find((c) => c.marker === marker || c.marker === parseInt(marker) || c.marker.toString() === marker);

          if (citation) {
            console.log("Converting citation to endnote:", citation);

            // Create endnote format
            let authorText = "Citation";
            let yearText = new Date().getFullYear().toString();

            // Try to extract author and year from the citation data
            if (citation.authors && citation.authors.length > 0) {
              authorText = citation.authors[0].split(" ").pop() || citation.marker;
            } else if (citation.reference) {
              // Parse the reference string to extract author and year
              const parts = citation.reference.split(",");
              if (parts.length > 0) {
                authorText = parts[0].trim() || citation.marker;
              }

              // Look for a year in the reference string
              const yearMatch = citation.reference.match(/\b(19|20)\d{2}\b/);
              if (yearMatch) {
                yearText = yearMatch[0];
              }
            } else {
              authorText = citation.marker;
            }

            // Use citation year if available
            if (citation.year) {
              yearText = citation.year;
            }

            console.log(`Creating endnote: (${authorText}, ${yearText})`);

            // Create a text node with the endnote format
            const endnoteNode = document.createTextNode(
              `(${authorText}, ${yearText})`,
            );

            // Replace the superscript element with the endnote
            if (supElement.parentNode) {
              supElement.parentNode.replaceChild(endnoteNode, supElement);
              console.log("Replaced superscript with endnote");
            }
          }
        }
      }
    } else if (fromType === "endnotes" && toType === "footnotes") {
      // Convert from endnotes to footnotes

      console.log("Converting endnotes to footnotes");

      // First, let's try a more direct approach targeting specific converted endnote patterns
      // For citations that were previously footnotes, look for the specific pattern (marker, YYYY)
      let convertedAny = false;

      // Create a map of all parenthetical patterns in the HTML
      const parentheticalMatches: {
        pattern: string;
        node: Node;
        marker?: string;
        citation?: Citation;
      }[] = [];

      // Process all text nodes to find potential parenthetical citations
      const textNodes: Text[] = [];
      const findTextNodes = (node: Node) => {
        if (node.nodeType === Node.TEXT_NODE) {
          // Check if this text node might contain a parenthetical citation
          const text = node.textContent || "";
          if (text.includes("(") && text.includes(")")) {
            const endnoteRegex = /\(([^,]+),\s*(\d{4})\)/g;
            let match;

            while ((match = endnoteRegex.exec(text)) !== null) {
              const pattern = match[0];
              const author = match[1].trim();
              const year = match[2];

              // First check for direct marker matches (from converted footnotes)
              // This is the most reliable way to match back to original citations
              // Use filteredCitations first (these are the ones actually in the content), then fall back to citations prop
              const citationsToSearch = filteredCitations.length > 0 ? filteredCitations : citations;
              console.log("Endnotes to footnotes conversion - using citations array:", citationsToSearch.length, "citations");
              let citationMatch = citationsToSearch.find((c) => c.marker === author || c.marker.toString() === author);

              // If no direct match, try other matching approaches
              if (!citationMatch) {
                citationMatch = citationsToSearch.find((c) => {
                  // Check for matching author and year
                  const matchesAuthor =
                    c.reference?.toLowerCase().includes(author.toLowerCase()) ||
                    (c.authors &&
                      c.authors.some((a) =>
                        a.toLowerCase().includes(author.toLowerCase()),
                      ));

                  const matchesYear =
                    c.year === year || c.reference?.includes(year);

                  return matchesAuthor && matchesYear;
                });
              }

              if (citationMatch) {
                console.log(
                  `Found endnote to convert: ${pattern} -> citation ${citationMatch.marker}`,
                );
                parentheticalMatches.push({
                  pattern,
                  node,
                  marker: author,
                  citation: citationMatch,
                });
              }
            }
          }

          textNodes.push(node as Text);
        } else {
          node.childNodes.forEach((child) => findTextNodes(child));
        }
      };

      findTextNodes(tempContainer);
      console.log(
        `Found ${textNodes.length} text nodes and ${parentheticalMatches.length} potential citations`,
      );

      // Now replace these parenthetical citations with superscript elements
      for (const { pattern, node, citation } of parentheticalMatches) {
        if (!citation) continue;

        const text = node.textContent || "";
        const patternIndex = text.indexOf(pattern);

        if (patternIndex >= 0) {
          // Create text nodes for the parts before and after the citation
          const beforeText = text.substring(0, patternIndex);
          const afterText = text.substring(patternIndex + pattern.length);

          // Create a superscript element for the footnote
          const supElement = document.createElement("sup");
          supElement.textContent = citation.marker;

          // Create new text nodes
          const beforeNode = document.createTextNode(beforeText);
          const afterNode = document.createTextNode(afterText);

          // Replace the original text node with our three new nodes
          if (node.parentNode) {
            node.parentNode.insertBefore(beforeNode, node);
            node.parentNode.insertBefore(supElement, node);
            node.parentNode.insertBefore(afterNode, node);
            node.parentNode.removeChild(node);
            convertedAny = true;
          }
        }
      }

      // If we didn't find any matches with the above approach, try a more aggressive approach
      if (!convertedAny) {
        console.log(
          "No endnotes converted with primary approach, trying fallback method",
        );

        // Find all parenthetical patterns in the content
        const contentCopy = tempContainer.innerHTML;
        const allParenthesesRegex = /\(([^)]+)\)/g;
        let match;

        // Find all full parenthetical patterns and try to match them to citations
        while ((match = allParenthesesRegex.exec(contentCopy)) !== null) {
          const fullPattern = match[0];
          const innerText = match[1];

          // Check if this could be a citation by looking for specific patterns
          // 1. Contains a year (4 digit number)
          const yearMatch = innerText.match(/\b(\d{4})\b/);

          // 2. Contains a citation marker or author name
          let potentialCitation: Citation | undefined;

          // Use the same citations array as above
          const citationsToSearch = filteredCitations.length > 0 ? filteredCitations : citations;
          for (const citation of citationsToSearch) {
            // Direct marker match
            if (innerText.includes(citation.marker)) {
              potentialCitation = citation;
              break;
            }

            // Author match
            if (
              citation.authors &&
              citation.authors.some((author) => {
                const lastName = author.split(" ").pop() || "";
                return innerText.includes(lastName);
              })
            ) {
              potentialCitation = citation;
              break;
            }
          }

          if (potentialCitation && yearMatch) {
            console.log(
              `Found potential endnote pattern: ${fullPattern} -> citation ${potentialCitation.marker}`,
            );

            // Try to find this pattern in the DOM and replace it
            for (const textNode of textNodes) {
              const nodeText = textNode.textContent || "";
              if (nodeText.includes(fullPattern)) {
                // Split the text and replace the pattern with a superscript
                const parts = nodeText.split(fullPattern);

                if (parts.length >= 2) {
                  // Create new nodes
                  const beforeNode = document.createTextNode(parts[0]);
                  const supElement = document.createElement("sup");
                  supElement.textContent = potentialCitation.marker;
                  const afterNode = document.createTextNode(
                    parts.slice(1).join(fullPattern),
                  );

                  // Replace the original node
                  if (textNode.parentNode) {
                    textNode.parentNode.insertBefore(beforeNode, textNode);
                    textNode.parentNode.insertBefore(supElement, textNode);
                    textNode.parentNode.insertBefore(afterNode, textNode);
                    textNode.parentNode.removeChild(textNode);
                    convertedAny = true;
                    break; // Process one match at a time
                  }
                }
              }
            }
          }
        }
      }

      console.log(
        `Conversion complete, converted any citations: ${convertedAny}`,
      );
    }

    // Get the updated content with the converted citations
    updatedContent = tempContainer.innerHTML;

    return updatedContent;
  };

  // Toggle between footnotes and endnotes
  const toggleNotesType = () => {
    const oldNotesType = notesType;
    const newNotesType =
      oldNotesType === "footnotes" ? "endnotes" : "footnotes";

    console.log(`Toggling notes type from ${oldNotesType} to ${newNotesType}`);

    // Convert all citations in the current content
    const updatedContent = convertAllCitations(
      content,
      oldNotesType,
      newNotesType,
    );

    // Update the content with converted citations
    if (updatedContent !== content) {
      console.log("Updating content with converted citations");
      onContentChange(updatedContent);
    }

    // Update the notes type
    if (onNotesTypeChange) {
      onNotesTypeChange(newNotesType);
    } else {
      setInternalNotesType(newNotesType);
    }
  };

  // Filter notes by active outline item
  useEffect(() => {
    if (activeOutlineItemId) {
      // Filter notes linked to the active outline item
      const filtered = notes.filter((note) =>
        note.linkedOutlineId === activeOutlineItemId
      );
      setFilteredNotes(filtered);
    } else {
      setFilteredNotes([]);
    }
  }, [activeOutlineItemId, notes]);

  // Filter references by selected collection
  useEffect(() => {
    if (!allUserReferences || allUserReferences.length === 0) return;

    // Create references for display with consistent marker numbers
    let referencesToShow: Citation[] = [];

    if (selectedCollectionId === "all") {
      // Show all references
      referencesToShow = allUserReferences.map((ref, index) => ({
        ...ref,
        id: ref.id || nanoid(),
        marker: String(index + 1),
        // Ensure all necessary properties for citation are present
        title: ref.title || (ref.reference ? ref.reference.split(".")[0] : ""),
        authors: ref.authors || [],
        year: ref.year || new Date().getFullYear().toString(),
        reference: ref.reference || `Citation ${index + 1}`,
      }));
    } else if (selectedCollectionId === "uncategorized") {
      // Show references without a collection ID
      referencesToShow = allUserReferences
        .filter((ref) => !ref.collectionId)
        .map((ref, index) => ({
          ...ref,
          id: ref.id || `citation-${nanoid()}`,
          marker: String(index + 1),
          // Ensure all necessary properties for citation are present
          title:
            ref.title || (ref.reference ? ref.reference.split(".")[0] : ""),
          authors: ref.authors || [],
          year: ref.year || new Date().getFullYear().toString(),
          reference: ref.reference || `Citation ${index + 1}`,
        }));
    } else {
      // Show references from the selected collection
      referencesToShow = allUserReferences
        .filter((ref) => ref.collectionId === selectedCollectionId)
        .map((ref, index) => ({
          ...ref,
          id: ref.id || `collection-${nanoid()}`,
          marker: String(index + 1),
          // Ensure all necessary properties for citation are present
          title:
            ref.title || (ref.reference ? ref.reference.split(".")[0] : ""),
          authors: ref.authors || [],
          year: ref.year || new Date().getFullYear().toString(),
          reference: ref.reference || `Citation ${index + 1}`,
        }));
    }

    // Log the processed references
    console.log("Processed references for display:", referencesToShow);

    setDisplayReferences(referencesToShow);
  }, [selectedCollectionId, allUserReferences]);

  // Auto-populate footnotes/endnotes from citations used in the text
  useEffect(() => {
    if (!content || !activeOutlineItemId) {
      setFilteredCitations([]);
      return;
    }

    // Debug log to check content updates
    console.log(
      "Analyzing content for citations:",
      content.substring(0, 100) + "...",
    );

    // We need to handle two modes differently
    if (notesType === "footnotes") {
      const usedCitations: Citation[] = [];

      // 1. Create a temporary container to parse the HTML content
      const tempContainer = document.createElement("div");
      tempContainer.innerHTML = content;

      // 2. Find all sup elements in the content - these are our footnote markers
      const supElements = tempContainer.querySelectorAll("sup");
      console.log(
        `Found ${supElements.length} superscript elements in content`,
      );

      // 3. Process each found superscript element
      const markers: string[] = [];
      supElements.forEach((sup) => {
        // Extract the marker number from the sup element
        const marker = sup.textContent?.trim();

        if (marker && /^\d+$/.test(marker)) {
          markers.push(marker);

          // First try to find by reference ID (from data attribute)
          const referenceId = sup.getAttribute("data-reference-id");
          let matchingReference = null;

          if (referenceId) {
            // Look for a reference with this ID in allUserReferences
            matchingReference = allUserReferences?.find((ref) => ref.id === referenceId);
          }

          // Fallback to marker matching in citations if no reference ID found
          let citation = null;
          if (!matchingReference) {
            citation = citations.find((c) => c.marker === marker);
          }

          // If no matching reference or citation found, try to assign the first available reference
          // This handles orphaned footnote markers that don't have reference IDs
          if (!matchingReference && !citation && allUserReferences && allUserReferences.length > 0) {
            // Use the first available reference as a fallback
            matchingReference = allUserReferences[0];
            console.log('Using fallback reference for orphaned footnote marker:', matchingReference);
          }

          if (matchingReference || citation) {
            // Create an enhanced citation with all available data
            // Note: We no longer check for duplicates - each instance gets its own entry
            let enhancedCitation: Citation;

            if (matchingReference) {
              // Create citation from reference data with unique ID for this instance
              // Renumber sequentially starting from 1
              enhancedCitation = {
                id: `${matchingReference.id}-instance-${usedCitations.length}`,
                marker: (usedCitations.length + 1).toString(),
                reference: `${matchingReference.authors?.join(', ') || 'Unknown'} ${matchingReference.year || ''}. ${matchingReference.title || ''}`,
                type: matchingReference.type as any || "other",
                title: matchingReference.title || "",
                authors: matchingReference.authors || [],
                year: matchingReference.year || "",
                url: matchingReference.url || "",
                doi: matchingReference.doi || "",
                publisher: matchingReference.publisher || "",
                journal: matchingReference.journal || "",
                volume: matchingReference.volume || "",
                issue: matchingReference.issue || "",
                pages: matchingReference.pages || "",
                collectionId: matchingReference.collectionId,
              };
            } else if (citation) {
              // Look up additional reference data from our available references
              const referenceInfo = allUserReferences?.find(
                (ref) =>
                  ref.title === citation.title ||
                  (ref.authors?.length &&
                    citation.authors &&
                    Array.isArray(citation.authors) &&
                    citation.authors.length > 0 &&
                    ref.authors.some((a) => citation.authors.includes(a))),
              );

              // Create an enhanced citation with all available data and unique ID for this instance
              // Renumber sequentially starting from 1
              enhancedCitation = {
                ...citation,
                id: `${citation.id}-instance-${usedCitations.length}`,
                marker: (usedCitations.length + 1).toString(),
                // Merge in reference data from our reference manager if available
                ...(referenceInfo
                  ? {
                      title: referenceInfo.title || citation.title,
                      authors: referenceInfo.authors || citation.authors,
                      year: referenceInfo.year || citation.year,
                      publisher: referenceInfo.publisher || citation.publisher,
                      journal: referenceInfo.journal || citation.journal,
                      volume: referenceInfo.volume || citation.volume,
                      issue: referenceInfo.issue || citation.issue,
                      pages: referenceInfo.pages || citation.pages,
                      url: referenceInfo.url || citation.url,
                      doi: referenceInfo.doi || citation.doi,
                      reference: referenceInfo.reference || citation.reference,
                    }
                  : {}),
              };
            } else {
              return; // Skip if no matching reference or citation found
            }

            console.log("Enhanced citation for footnote:", enhancedCitation);
            usedCitations.push(enhancedCitation);
          }
        }
      });

      console.log("Found footnote markers in content:", markers);
      console.log("Matching citations:", usedCitations.length);

      // Update HTML content to reflect sequential numbering - DIRECT DOM UPDATE
      if (usedCitations.length > 0) {
        // Get the actual editor element for direct DOM manipulation
        const editorElement = document.getElementById("editor")?.querySelector('[contenteditable="true"]') ||
                             document.querySelector('#editor [contenteditable="true"]') ||
                             document.querySelector('[contenteditable="true"]');

        if (editorElement) {
          const supElements = editorElement.querySelectorAll("sup");
          let sequentialIndex = 0;
          let hasChanges = false;

          supElements.forEach((sup) => {
            const marker = sup.textContent?.trim();
            if (marker && /^\d+$/.test(marker)) {
              sequentialIndex++;
              const newMarker = sequentialIndex.toString();
              if (sup.textContent !== newMarker) {
                console.log(`🔄 DIRECT UPDATE: Renumbering footnote marker: ${marker} → ${newMarker}`);
                sup.textContent = newMarker;
                hasChanges = true;
              }
            }
          });

          if (hasChanges) {
            console.log("✅ Direct DOM update completed - footnote markers renumbered");
            // Also update the content state to keep it in sync
            setTimeout(() => {
              const updatedContent = editorElement.innerHTML;
              onContentChange(updatedContent);
            }, 100);
          }
        } else {
          console.log("⚠️ Editor element not found for direct DOM update");
        }
      }

      // If we didn't find any with DOM parsing, fall back to regex as a secondary approach
      if (usedCitations.length === 0) {
        console.log("DOM parsing found no citations, trying regex fallback");

        // This will match both <sup>1</sup> and <sup>[1]</sup> patterns
        const regex = /<sup>\s*(?:\[)?\s*(\d+)\s*(?:\])?\s*<\/sup>/g;
        let match;
        let contentCopy = content;

        while ((match = regex.exec(contentCopy)) !== null) {
          const marker = match[1].trim();
          console.log("Found footnote marker with regex:", marker);

          // Find citation with this marker
          const citation = citations.find((c) => c.marker === marker);
          if (citation) {
            // Note: We no longer check for duplicates - each instance gets its own entry
            // Look up additional reference data from our available references
            const referenceInfo = allUserReferences?.find(
              (ref) =>
                ref.title === citation.title ||
                (ref.authors?.length &&
                  citation.authors &&
                  Array.isArray(citation.authors) &&
                  citation.authors.length > 0 &&
                  ref.authors.some((a) => citation.authors.includes(a))),
            );

            // Create an enhanced citation with all available data and unique ID for this instance
            // Renumber sequentially starting from 1
            const enhancedCitation: Citation = {
              ...citation,
              id: `${citation.id}-instance-${usedCitations.length}`,
              marker: (usedCitations.length + 1).toString(),
              // Merge in reference data from our reference manager if available
              ...(referenceInfo
                ? {
                    title: referenceInfo.title || citation.title,
                    authors: referenceInfo.authors || citation.authors,
                    year: referenceInfo.year || citation.year,
                    publisher: referenceInfo.publisher || citation.publisher,
                    journal: referenceInfo.journal || citation.journal,
                    volume: referenceInfo.volume || citation.volume,
                    issue: referenceInfo.issue || citation.issue,
                    pages: referenceInfo.pages || citation.pages,
                    url: referenceInfo.url || citation.url,
                    doi: referenceInfo.doi || citation.doi,
                    reference: referenceInfo.reference || citation.reference,
                  }
                : {}),
            };

            console.log(
              "Enhanced citation from regex fallback:",
              enhancedCitation,
            );
            usedCitations.push(enhancedCitation);
          }
        }

        console.log(
          "After regex fallback, found citations:",
          usedCitations.length,
        );
      }

      // Set the filtered citations for display
      setFilteredCitations(usedCitations);
    } else {
      // For endnotes: Extract parenthetical citations (Author, YYYY)
      const usedCitations: Citation[] = [];

      // 1. Create a temporary container for parsing HTML
      const tempContainer = document.createElement("div");
      tempContainer.innerHTML = content;

      // 2. First check text nodes for parenthetical patterns
      const textNodes: Text[] = [];
      const findTextNodes = (node: Node) => {
        if (node.nodeType === Node.TEXT_NODE) {
          textNodes.push(node as Text);
        } else {
          node.childNodes.forEach((child) => findTextNodes(child));
        }
      };

      findTextNodes(tempContainer);
      console.log(
        `Found ${textNodes.length} text nodes to scan for endnote citations`,
      );

      // 3. Check each text node for parenthetical citations
      const foundCitations: Array<{ author: string; year: string }> = [];
      const regex = /\(([^,]+),\s*(\d{4})\)/g;

      console.log("Searching for endnotes in text nodes");

      textNodes.forEach((textNode) => {
        const text = textNode.textContent || "";
        if (text.includes("(") && text.includes(")")) {
          console.log("Found potential endnote text:", text);
        }

        let match;

        while ((match = regex.exec(text)) !== null) {
          const author = match[1].trim();
          const year = match[2];
          console.log(
            "Found endnote pattern:",
            match[0],
            "- Author:",
            author,
            "Year:",
            year,
          );
          foundCitations.push({ author, year });

          // Search for citations that match this pattern
          for (const citation of citations) {
            // First, check for exact citation marker match (important for citations converted from footnotes)
            if (author === citation.marker) {
              console.log("Found exact marker match:", citation.marker);
              if (!usedCitations.some((c) => c.id === citation.id)) {
                usedCitations.push(citation);
              }
              continue;
            }

            // Check if it's a numeric citation like (1, 2022)
            if (/^\d+$/.test(author) && author === citation.marker) {
              console.log("Found numeric marker match:", citation.marker);
              if (!usedCitations.some((c) => c.id === citation.id)) {
                usedCitations.push(citation);
              }
              continue;
            }

            // Check if citation contains author name and year
            const matchesAuthor =
              citation.reference
                ?.toLowerCase()
                .includes(author.toLowerCase()) ||
              (citation.authors &&
                citation.authors.some((a) =>
                  a.toLowerCase().includes(author.toLowerCase()),
                ));

            const matchesYear =
              citation.year === year || citation.reference?.includes(year);

            if (matchesAuthor && matchesYear) {
              console.log(
                "Found author/year match:",
                author,
                year,
                "Citation:",
                citation.reference,
              );
              if (!usedCitations.some((c) => c.id === citation.id)) {
                usedCitations.push(citation);
              }
            }
          }
        }
      });

      // If no citations found with the standard pattern, try a more permissive approach
      if (usedCitations.length === 0) {
        console.log("No endnotes found with standard pattern, trying fallback");

        // Look for any parenthetical expressions in the content
        const allParenthesesRegex = /\(([^)]+)\)/g;
        let contentCopy = content;
        let parenthesesMatch;

        while (
          (parenthesesMatch = allParenthesesRegex.exec(contentCopy)) !== null
        ) {
          const parenthesesContent = parenthesesMatch[1];
          console.log("Found parentheses content:", parenthesesContent);

          // Check each citation to see if there's a match
          // Use filteredCitations first (these are the ones actually in the content), then fall back to citations prop
          const citationsToSearch = filteredCitations.length > 0 ? filteredCitations : citations;
          console.log("Endnotes detection - filteredCitations:", filteredCitations.length, "citations:", citations.length);
          console.log("Using citations array:", citationsToSearch.length, "citations");
          for (const citation of citationsToSearch) {
            // Check if this parenthetical content contains the citation marker
            if (parenthesesContent.includes(citation.marker)) {
              console.log(
                "Parentheses contains citation marker:",
                citation.marker,
              );
              if (!usedCitations.some((c) => c.id === citation.id)) {
                usedCitations.push(citation);
              }
              continue;
            }

            // Check if it contains author name
            if (
              citation.authors &&
              citation.authors.some((author) => {
                const lastName = author.split(" ").pop() || "";
                return parenthesesContent.includes(lastName);
              })
            ) {
              console.log("Parentheses contains author:", citation.authors);
              if (!usedCitations.some((c) => c.id === citation.id)) {
                usedCitations.push(citation);
              }
              continue;
            }

            // Check if it contains year
            if (citation.year && parenthesesContent.includes(citation.year)) {
              console.log("Parentheses contains year:", citation.year);
              if (!usedCitations.some((c) => c.id === citation.id)) {
                usedCitations.push(citation);
              }
            }
          }
        }
      }

      console.log("Found endnote patterns in content:", foundCitations);
      console.log("Matching citations:", usedCitations.length);

      // If DOM parsing didn't find any citations, fall back to regex on the raw HTML
      if (usedCitations.length === 0) {
        // Fallback to direct regex on the raw content
        let match;
        const contentRegex = /\(([^,]+),\s*(\d{4})\)/g;

        while ((match = contentRegex.exec(content)) !== null) {
          const author = match[1].trim();
          const year = match[2];

          // Search for matching citations as before...
          // Use filteredCitations first (these are the ones actually in the content), then fall back to citations prop
          const citationsToSearch = filteredCitations.length > 0 ? filteredCitations : citations;
          for (const citation of citationsToSearch) {
            // Same logic as above to find matching citations
            if (/^\d+$/.test(author) && author === citation.marker) {
              if (!usedCitations.some((c) => c.id === citation.id)) {
                usedCitations.push(citation);
              }
              continue;
            }

            const matchesAuthor =
              citation.reference
                ?.toLowerCase()
                .includes(author.toLowerCase()) ||
              (citation.authors &&
                citation.authors.some((a) =>
                  a.toLowerCase().includes(author.toLowerCase()),
                ));

            const matchesYear =
              citation.year === year || citation.reference?.includes(year);

            if (
              matchesAuthor &&
              matchesYear &&
              !usedCitations.some((c) => c.id === citation.id)
            ) {
              usedCitations.push(citation);
            }
          }
        }

        console.log(
          "After regex fallback for endnotes, found citations:",
          usedCitations.length,
        );

        // Handle synthetic citations based on detected patterns
        if (foundCitations.length === 0) {
          // If no patterns are found in the current content, clear all citations
          console.log("No endnote patterns found in content, clearing all citations");
          setFilteredCitations([]); // Directly clear the state
          return; // Exit early to prevent further processing
        } else if (usedCitations.length === 0) {
          // If we detected endnote patterns but haven't found matching citations,
          // create synthetic citations from the patterns, but deduplicate them
          console.log("Creating synthetic citations from detected endnote patterns");

          // Create synthetic citations for ALL instances (no deduplication)
          foundCitations.forEach((pattern, index) => {
            const syntheticCitation = {
              id: `synthetic-endnote-${index}`,
              marker: (usedCitations.length + 1).toString(),
              reference: `${pattern.author}, ${pattern.year}`,
              authors: [pattern.author],
              year: pattern.year,
              url: "",
              synthetic: true // Mark as synthetic so we know it was created from pattern detection
            };
            console.log("Created synthetic citation:", syntheticCitation);
            usedCitations.push(syntheticCitation);
          });

          console.log(`Created ${foundCitations.length} citation instances (no deduplication)`);
        }
      }

      // Set the filtered citations for display
      setFilteredCitations(usedCitations);
    }

    // If we still don't have any citations in filtered list, show some feedback
    if (citations.length > 0 && filteredCitations.length === 0) {
      console.log(
        "No citations found in content, but citations exist. Showing all citations as fallback.",
      );
    }
  }, [
    content,
    citations,
    notesType,
    activeOutlineItemId,
    filteredCitations.length,
  ]);

  // Create a flattened list of outline items for display
  const flattenedOutlineItems: OutlineItem[] = [];
  const flattenOutline = (items: OutlineItem[] = [], level = 0) => {
    items.forEach((item) => {
      flattenedOutlineItems.push({
        ...item,
        title: "  ".repeat(level) + item.title,
      });
      if (item.children && item.children.length > 0) {
        flattenOutline(item.children, level + 1);
      }
    });
  };
  flattenOutline(outline);

  return (
    <div className="panel relative flex-1 flex flex-col overflow-hidden">
      {/* Fixed header - same height as other panels (44px) */}
      <div className="bg-background flex justify-between items-center px-3 py-2 min-h-[44px] border-b border-border z-10">
        <h2 className="font-medium text-sm sm:text-base text-foreground flex items-center">
          <i className="ri-file-text-line mr-1.5 text-muted-foreground"></i>
          <span>Writing</span>
        </h2>
        <div className="flex gap-1">
          {onExport && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs px-2"
              onClick={onExport}
            >
              <i className="ri-download-2-line text-sm"></i>
            </Button>
          )}
          {isPoppedOut ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs px-2"
              title="Pop in panel"
              onClick={() => window.close()}
            >
              <i className="ri-login-box-line text-sm"></i>
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs px-2"
              title="Pop out panel"
              onClick={popOutPanel}
            >
              <i className="ri-external-link-line text-sm"></i>
            </Button>
          )}
        </div>
      </div>

      {/* Content area */}
      <div className="flex items-center p-2 border-b border-border bg-muted/50">
        <div className="w-3/5 flex-grow pr-2">
          <div className="relative">
            <select
              className="w-full rounded-md border border-input bg-background px-3 py-1.5 text-sm 
                       shadow-sm focus:border-primary focus:outline-none appearance-none"
              value={activeOutlineItemId || ""}
              onChange={(e) => onOutlineItemSelect(e.target.value)}
            >
              <option value="" disabled>
                Select a section
              </option>
              {flattenedOutlineItems.map((item: OutlineItem) => (
                <option key={item.id} value={item.id}>
                  {item.number ? `${item.number} ${item.title}` : item.title}
                </option>
              ))}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
              <i className="ri-arrow-down-s-line text-muted-foreground"></i>
            </div>
          </div>
        </div>
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="outline"
            className="h-7 text-xs px-2 flex items-center border-[var(--icon-amber)] bg-[var(--icon-amber)]/10 text-[var(--icon-amber)] hover:bg-[var(--icon-amber)]/20"
            onClick={toggleNotesType}
            disabled={!activeOutlineItemId}
          >
            <i
              className={`ri-${notesType === "footnotes" ? "superscript" : "text-wrap"} mr-1`}
            ></i>
            {notesType === "footnotes" ? "Footnotes" : "Endnotes"}
          </Button>
          <Button
            className="h-7 text-xs px-2 bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={onCheckText}
            disabled={!activeOutlineItemId}
          >
            <i className="ri-check-line mr-1"></i>
            Check Text
          </Button>
        </div>
      </div>

      <div className="flex flex-1 min-h-0 overflow-hidden">
        {/* Main content area with editor */}
        <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
          {activeOutlineItemId ? (
            <div
              className="flex-1 border-0"
              id="editor"
              style={{ border: "none", height: "100%" }}
            >
              <AiEnhancedRichTextEditor
                ref={editorRef}
                value={content}
                onChange={onContentChange}
                className="border-0"
                onShowSubscriptionModal={onShowSubscriptionModal}
                fillContainer={true}
              />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-muted/50">
              <div className="text-center p-6 max-w-md">
                <i className="ri-file-text-line text-4xl text-muted-foreground/50 mb-2"></i>
                <h3 className="text-lg font-medium text-foreground">
                  Select a section to begin
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a section from the dropdown above to start writing
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Right sidebar for tools */}
        <div className="w-56 flex-shrink-0 border-l border-border bg-muted/50 flex flex-col">
          {/* Citations section */}
          <div className="p-2 border-b border-border flex-1 overflow-auto">
            <h3 className="font-medium text-sm mb-2">
              <span>Citations</span>
            </h3>

            {/* Notice for edit guests */}
            {isEditGuest && (
              <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                <div className="flex items-start gap-2">
                  <i className="ri-information-line text-blue-600 dark:text-blue-400 text-sm mt-0.5"></i>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    Citations you add will be imported to the document owner's reference manager.
                  </p>
                </div>
              </div>
            )}

            {/* Read-only notice for view-only guests */}
            {isReadOnlyGuest && (
              <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md">
                <div className="flex items-start gap-2">
                  <i className="ri-lock-line text-gray-600 dark:text-gray-400 text-sm mt-0.5"></i>
                  <p className="text-xs text-gray-700 dark:text-gray-300">
                    Citation adding is disabled with view-only access.
                  </p>
                </div>
              </div>
            )}

            {/* Collection selector */}
            <div className="mb-3 relative">
              <select
                className="w-full rounded-md border border-input bg-background px-3 py-1.5 text-xs
                         shadow-sm focus:border-[var(--icon-purple)] focus:outline-none appearance-none"
                value={selectedCollectionId}
                onChange={(e) => setSelectedCollectionId(e.target.value)}
              >
                {allCollections.map((collection) => (
                  <option key={collection.id} value={collection.id}>
                    {collection.name}
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                <i className="ri-arrow-down-s-line text-muted-foreground"></i>
              </div>
            </div>

            {/* Drag hint */}
            <div className="flex items-center gap-1 px-2 py-1.5 mb-2 bg-[var(--icon-purple)]/10 border border-[var(--icon-purple)]/20 rounded-md">
              <i className="ri-drag-move-line text-icon-purple"></i>
              <p className="text-xs text-icon-purple">
                Drag citations to your document
              </p>
            </div>

            <ScrollArea className="h-[calc(100%-7.5rem)]">
              {!isLoadingReferences && displayReferences.length > 0 ? (
                <div className="space-y-2">
                  {displayReferences.map((reference, index) => (
                    <div
                      key={`${reference.id}-${index}`}
                      className={`${
                        canAddCitations
                          ? "cursor-grab active:cursor-grabbing hover:bg-accent/50"
                          : "cursor-not-allowed opacity-60"
                      } rounded-md`}
                      draggable={canAddCitations}
                      onDragStart={canAddCitations ? (e) => {
                        // Generate a marker for this reference based on existing citations
                        // Get current writing content to count existing citations
                        const currentWriting = getCurrentWriting ? getCurrentWriting() : { content: '', citations: [] };
                        const existingCitations = currentWriting.citations || [];
                        const nextMarker = (existingCitations.length + 1).toString();

                        // When dragging, include the current note type preference to ensure
                        // the citation is formatted correctly when dropped
                        // Also include all possible metadata fields for a complete citation record
                        const citationWithFormat = {
                          ...reference,
                          marker: nextMarker, // Generate marker for this drag operation
                          isFootnote: notesType === "footnotes",
                          // Make sure these fields are populated
                          title:
                            reference.title ||
                            (reference.reference
                              ? reference.reference.split(".")[0]
                              : ""),
                          authors: reference.authors || [],
                          year:
                            reference.year ||
                            new Date().getFullYear().toString(),
                          reference:
                            reference.reference ||
                            `Citation ${nextMarker}`,
                        };

                        e.dataTransfer.setData("text/plain", nextMarker);
                        e.dataTransfer.setData(
                          "application/json",
                          JSON.stringify(citationWithFormat),
                        );

                        console.log(
                          "Setting drag data with complete reference:",
                          citationWithFormat,
                        );
                      } : undefined}
                      onClick={canAddCitations ? () => insertCitationIntoEditor(reference) : undefined}
                      title={!canAddCitations ? "Citation adding is disabled for view-only access" : undefined}
                    >
                      <CitationComponent
                        citation={reference}
                        onUpdate={onUpdateCitation}
                        onDelete={canAddCitations ? (citationId: string) => {
                          // Find the citation object by ID to pass to handleDeleteCitation
                          const citationToDelete = displayReferences.find(ref => ref.id === citationId);
                          if (citationToDelete) {
                            handleDeleteCitation(citationToDelete);
                          } else {
                            // Fallback to direct deletion if citation not found
                            onDeleteCitation(citationId);
                          }
                        } : () => {}} // No-op function for read-only guests
                        isDraggable={canAddCitations}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-xs text-muted-foreground">
                  {isLoadingReferences
                    ? "Loading references..."
                    : selectedCollectionId === "all"
                      ? "No references available"
                      : "No references in this collection"}
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Display footnotes/endnotes */}
          <div
            className="p-2 border-b border-border flex flex-col"
            style={{ maxHeight: "30vh" }}
          >
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium text-sm">
                {notesType === "footnotes" ? "Footnotes" : "Endnotes"}
              </h3>
              <span className="text-xs text-muted-foreground">
                {filteredCitations.length}{" "}
                {filteredCitations.length === 1 ? "citation" : "citations"}
              </span>
            </div>

            <ScrollArea
              className="pr-2"
              style={{ maxHeight: "calc(30vh - 2rem)" }}
              onMouseLeave={() => removeHighlighting()}
            >
              <div className="text-xs space-y-2">
                {filteredCitations.length > 0 ? (
                  filteredCitations.map((citation) => (
                    <div
                      key={citation.id}
                      className="flex gap-2 p-1.5 rounded-md hover:bg-accent/30 group cursor-pointer"
                      onMouseEnter={() => highlightCitationInEditor(citation)}
                      onMouseLeave={() => removeHighlighting()}
                      onClick={() => scrollToCitation(citation)}
                      title="Click to scroll to citation in document"
                    >
                      <span className="font-medium text-primary min-w-[1.5rem]">
                        [{citation.marker}]
                      </span>
                      <div className="flex-1">
                        {citation.title ? (
                          <>
                            <div className="text-foreground font-medium">
                              {citation.title}
                            </div>
                            <div className="text-muted-foreground text-[0.7rem] mt-0.5">
                              {citation.authors && citation.authors.length > 0
                                ? citation.authors.join(", ")
                                : citation.reference.split(".")[0]}
                              {citation.year && `, ${citation.year}`}
                              {citation.publisher && `. ${citation.publisher}`}
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="text-foreground">
                              {citation.reference.length > 80
                                ? citation.reference.substring(0, 80) + "..."
                                : citation.reference}
                            </div>
                            {citation.authors &&
                              citation.authors.length > 0 && (
                                <div className="text-muted-foreground text-[0.7rem] mt-0.5">
                                  {citation.authors.join(", ")}
                                  {citation.year && `, ${citation.year}`}
                                </div>
                              )}
                          </>
                        )}
                        {citation.url && (
                          <div className="text-[0.7rem] text-primary mt-0.5 truncate max-w-[200px]">
                            {citation.url}
                          </div>
                        )}
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          className="text-muted-foreground hover:text-destructive"
                          onClick={() => handleDeleteCitation(citation)}
                          title="Remove all instances of this citation"
                        >
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    No {notesType} used in this section
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>

          {/* Word count */}
          <div className="px-2 py-1.5 flex gap-2 text-xs text-center text-muted-foreground border-t border-border">
            <span>Words: {content.split(/\s+/).length}</span>
            <span>|</span>
            <span>Chars: {content.length}</span>
          </div>
        </div>
      </div>

      {/* Citation Deletion Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={cancelDeleteCitation}
        onConfirm={confirmDeleteCitation}
        title="Remove Citation"
        description={
          citationToDelete
            ? `Are you sure you want to remove all instances of this citation from the text?\n\nCitation: ${citationToDelete.reference || `[${citationToDelete.marker}]`}\n\nThis action cannot be undone.`
            : "Are you sure you want to remove this citation?"
        }
        confirmText="Remove Citation"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  );
}
