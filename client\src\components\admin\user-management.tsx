import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2, Plus, UserPlus, Crown, UserX, User } from "lucide-react";
import { useAdminUsers, useCreateUser, useUpdateUserStatus, useUpdateUserPremium } from "@/hooks/use-admin-api";
import { User as UserType } from "@shared/schema";

export function UserManagement() {
  const { data: users, isLoading, error } = useAdminUsers();
  const createUserMutation = useCreateUser();
  const updateStatusMutation = useUpdateUserStatus();
  const updatePremiumMutation = useUpdateUserPremium();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    username: "",
    email: "",
    password: "",
    isPremium: false,
  });

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newUser.username || !newUser.password) return;

    try {
      await createUserMutation.mutateAsync(newUser);
      setNewUser({ username: "", email: "", password: "", isPremium: false });
      setIsCreateDialogOpen(false);
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleStatusToggle = async (userId: number, currentStatus: boolean) => {
    await updateStatusMutation.mutateAsync({
      userId,
      isActive: !currentStatus,
    });
  };

  const handlePremiumToggle = async (userId: number, currentPremium: boolean) => {
    await updatePremiumMutation.mutateAsync({
      userId,
      isPremium: !currentPremium,
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center text-red-600">
        Error loading users: {error.message}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          User Management
        </CardTitle>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Create New User
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateUser} className="space-y-4">
              <div>
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={newUser.username}
                  onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                  placeholder="Enter username"
                  required
                />
              </div>
              <div>
                <Label htmlFor="email">Email (Optional)</Label>
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <PasswordInput
                  id="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                  placeholder="Enter password"
                  required
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="premium"
                  checked={newUser.isPremium}
                  onCheckedChange={(checked) => setNewUser({ ...newUser, isPremium: checked })}
                />
                <Label htmlFor="premium">Premium Account</Label>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createUserMutation.isPending}
                >
                  {createUserMutation.isPending && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Create User
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Username</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Premium</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users?.map((user: UserType) => (
              <TableRow key={user.id}>
                <TableCell className="font-mono">{user.id}</TableCell>
                <TableCell className="font-medium">
                  {user.username}
                  {user.id === 1 && (
                    <Badge variant="secondary" className="ml-2">
                      Admin
                    </Badge>
                  )}
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {user.email || "No email"}
                </TableCell>
                <TableCell>
                  <Badge variant={user.isActive ? "default" : "destructive"}>
                    {user.isActive ? "Active" : "Disabled"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={user.isPremium ? "default" : "outline"}>
                    {user.isPremium ? (
                      <>
                        <Crown className="h-3 w-3 mr-1" />
                        Premium
                      </>
                    ) : (
                      "Free"
                    )}
                  </Badge>
                </TableCell>
                <TableCell>
                  {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : "N/A"}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    {user.id !== 1 && ( // Don't allow disabling admin user
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={user.isActive}
                          onCheckedChange={() => handleStatusToggle(user.id, user.isActive)}
                          disabled={updateStatusMutation.isPending}
                        />
                        <span className="text-sm text-muted-foreground">
                          {user.isActive ? "Active" : "Disabled"}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={user.isPremium}
                        onCheckedChange={() => handlePremiumToggle(user.id, user.isPremium)}
                        disabled={updatePremiumMutation.isPending}
                      />
                      <span className="text-sm text-muted-foreground">Premium</span>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
