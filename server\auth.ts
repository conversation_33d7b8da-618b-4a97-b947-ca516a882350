import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { storage } from "./storage";
import { User as SelectUser } from "@shared/schema";
import connectPg from "connect-pg-simple";
import { pool } from "./db";

const PostgresSessionStore = connectPg(session);

// SQL table creation script for sessions
const createSessionTableSQL = `
CREATE TABLE IF NOT EXISTS "session" (
  "sid" varchar NOT NULL COLLATE "default",
  "sess" json NOT NULL,
  "expire" timestamp(6) NOT NULL,
  CONSTRAINT "session_pkey" PRIMARY KEY ("sid")
);
CREATE INDEX IF NOT EXISTS "IDX_session_expire" ON "session" ("expire");
`;

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

// This type might be slightly off, but conceptually what we need for export
export let sessionStoreInstance: connectPg.PGStore;


export async function setupAuth(app: Express) {
  // Create session table if it doesn't exist
  try {
    await pool.query(createSessionTableSQL);
    console.log("Session table checked/created successfully");
  } catch (err) {
    console.error("Error creating session table:", err);
  }
  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET || "inspire-app-secret",
    resave: false,
    saveUninitialized: false,
    // store will be set below
    cookie: {
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      // Use 'lax' for nginx proxy setups - 'none' can cause issues
      sameSite: "lax",
      // Don't set domain to allow cookies to work with nginx proxy
      domain: undefined,
    },
    // Important for nginx proxy setups
    proxy: process.env.NODE_ENV === "production",
  };

  sessionStoreInstance = new PostgresSessionStore({ pool, createTableIfMissing: true });
  sessionSettings.store = sessionStoreInstance;


  // Trust nginx proxy - important for session cookies to work properly
  app.set("trust proxy", process.env.NODE_ENV === "production" ? 1 : false);

  // Log session configuration for debugging
  console.log("Session configuration:", {
    secure: sessionSettings.cookie?.secure,
    sameSite: sessionSettings.cookie?.sameSite,
    httpOnly: sessionSettings.cookie?.httpOnly,
    nodeEnv: process.env.NODE_ENV,
    secureCookies: process.env.SECURE_COOKIES
  });

  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        // Check if login is enabled (with emergency override)
        const emergencyOverride = process.env.EMERGENCY_LOGIN_OVERRIDE === "true";
        if (!emergencyOverride) {
          const loginSetting = await storage.getSystemSetting("login_enabled");
          if (loginSetting && loginSetting.value === "false") {
            return done(null, false, { message: "Login is currently disabled" });
          }
        }

        // Try to find user by username first, then by email
        let user = await storage.getUserByUsername(username);
        if (!user) {
          // If not found by username, try by email
          user = await storage.getUserByEmail(username);
        }

        if (!user || !(await comparePasswords(password, user.password))) {
          return done(null, false);
        }

        // Check if user account is active
        if (!user.isActive) {
          return done(null, false, { message: "Account is disabled" });
        }

        return done(null, user);
      } catch (err) {
        return done(err);
      }
    }),
  );

  passport.serializeUser((user, done) => done(null, user.id));
  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (err) {
      done(err);
    }
  });

  app.post("/api/register", async (req, res, next) => {
    try {
      // Check if registration is enabled
      const registrationSetting = await storage.getSystemSetting("registration_enabled");
      if (registrationSetting && registrationSetting.value === "false") {
        return res.status(403).json({ message: "Registration is currently disabled" });
      }

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(req.body.username);
      if (existingUser) {
        return res.status(400).send("Username already exists");
      }

      // Check if email already exists (if email is provided)
      if (req.body.email) {
        const existingEmailUser = await storage.getUserByEmail(req.body.email);
        if (existingEmailUser) {
          return res.status(400).send("Email already exists");
        }
      }

      const user = await storage.createUser({
        username: req.body.username,
        email: req.body.email || null,
        password: await hashPassword(req.body.password),
        isPremium: false,
        isActive: true,
      });

      req.login(user, (err) => {
        if (err) return next(err);
        res.status(201).json(user);
      });
    } catch (err) {
      next(err);
    }
  });

  app.post("/api/login", passport.authenticate("local"), (req, res) => {
    res.status(200).json(req.user);
  });

  app.post("/api/logout", (req, res, next) => {
    req.logout((err) => {
      if (err) return next(err);
      res.sendStatus(200);
    });
  });

  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    res.json(req.user);
  });
}