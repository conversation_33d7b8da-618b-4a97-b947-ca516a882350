import React, { useRef, useState, useEffect } from "react";
import { RichTextEditor } from "./rich-text-editor";
import { AiSuggestion } from "@/components/ai/context-ai-popup";
import { useToast } from "@/hooks/use-toast";
import { processAiSuggestion } from "@/services/ai-service";

interface AiEnhancedRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  onShowSubscriptionModal?: () => void;
  fillContainer?: boolean;
  readOnly?: boolean;
}

export const AiEnhancedRichTextEditor = React.forwardRef<HTMLDivElement, AiEnhancedRichTextEditorProps>(({
  value,
  onChange,
  className,
  onShowSubscriptionModal,
  fillContainer,
  readOnly = false,
  ...props
}, ref) => {
  const internalRef = useRef<HTMLDivElement>(null);
  const editorRef = ref || internalRef;
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Simplified AI suggestion handler
  const handleAiSuggestion = async (suggestion: AiSuggestion) => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      // Get current selection or use entire content
      const selection = window.getSelection();
      const selectedText = selection?.toString().trim() || value;

      const response = await processAiSuggestion(suggestion, selectedText);

      if (response.success) {
        if (selection && selection.toString().trim()) {
          // Replace selected text
          const newText = value.replace(selection.toString(), response.result || selectedText);
          onChange(newText);
        } else {
          // Apply to entire content
          onChange(response.result || value);
        }

        toast({
          title: suggestion.label,
          description: "AI suggestion applied successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to process AI suggestion",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div ref={editorRef} className={`relative ${fillContainer ? 'h-full' : ''}`}>
      <RichTextEditor
        value={value}
        onChange={onChange}
        className={className}
        fillContainer={fillContainer}
        readOnly={readOnly}
        {...props}
      />

      {/* Compact AI toolbar - icon-only buttons - hide when read-only */}
      {!readOnly && (
        <div className="absolute top-2 right-2 flex gap-1 z-20">
        <button
          onClick={() => handleAiSuggestion({ id: 'improve', icon: 'ri-magic-line', label: 'Improve Writing' })}
          className="w-6 h-6 flex items-center justify-center rounded text-xs bg-[#8056F6] text-white hover:bg-[#7046E6] border border-[#9066FF]/30 shadow-sm transition-all duration-300"
          title="Improve Writing"
        >
          <i className="ri-magic-line text-xs"></i>
        </button>
        <button
          onClick={() => handleAiSuggestion({ id: 'grammar', icon: 'ri-check-double-line', label: 'Fix Grammar' })}
          className="w-6 h-6 flex items-center justify-center rounded text-xs bg-[#8056F6] text-white hover:bg-[#7046E6] border border-[#9066FF]/30 shadow-sm transition-all duration-300"
          title="Fix Grammar"
        >
          <i className="ri-check-double-line text-xs"></i>
        </button>
        <button
          onClick={() => handleAiSuggestion({ id: 'summarize', icon: 'ri-file-text-line', label: 'Summarize' })}
          className="w-6 h-6 flex items-center justify-center rounded text-xs bg-[#8056F6] text-white hover:bg-[#7046E6] border border-[#9066FF]/30 shadow-sm transition-all duration-300"
          title="Summarize"
        >
          <i className="ri-file-text-line text-xs"></i>
        </button>
        </div>
      )}
    </div>
  );
});
