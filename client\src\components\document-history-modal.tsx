import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { History, RotateCcw, AlertCircle, Lock, Plus, Minus, Loader2, FileText, Edit3, StickyNote } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { ConfirmModal } from "@/components/ui/confirm-modal";
import { apiRequest, queryClient } from "@/lib/queryClient";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { computeDocumentDiff, DocumentDiff, SectionDiff, DiffLine } from "@/utils/document-diff";

// Legacy interface for backward compatibility
interface ChangedSection {
  type: 'outline' | 'note';
  id: string;
  title: string;
  changes: string[];
}

interface DocumentVersion {
  id: number;
  documentId: string;
  userId: number;
  versionNumber: number;
  changeDescription: string | null;
  createdAt: string;
  username: string;
  content?: DocumentContentData;
  addedLines?: number;
  removedLines?: number;
  changedSections?: ChangedSection[];
}

interface DocumentContentData {
  notes: Note[];
  outline: OutlineItem[];
}

interface Note {
  id: string;
  title: string;
  content: string;
  linkedOutlineIds?: string[];
  imageUrls?: string[];
  createdAt: string;
  updatedAt: string;
}

interface OutlineItem {
  id: string;
  title: string;
  children?: OutlineItem[];
}

// Use the new DocumentDiff type as DiffSummary
type DiffSummary = DocumentDiff;

// Component to render individual diff lines with git-style formatting
function DiffLineComponent({ line }: { line: DiffLine }) {
  const getLineStyle = () => {
    switch (line.type) {
      case 'added':
        return 'bg-green-50 text-green-800 border-l-2 border-green-500';
      case 'removed':
        return 'bg-red-50 text-red-800 border-l-2 border-red-500';
      case 'unchanged':
        return 'bg-gray-50 text-gray-700';
      case 'context':
        return 'bg-gray-50 text-gray-600';
      default:
        return '';
    }
  };

  const getLinePrefix = () => {
    switch (line.type) {
      case 'added':
        return '+';
      case 'removed':
        return '-';
      case 'unchanged':
        return ' ';
      case 'context':
        return ' ';
      default:
        return '';
    }
  };

  return (
    <div className={`px-2 py-1 font-mono text-xs ${getLineStyle()}`}>
      <span className="inline-block w-4 text-center font-bold">
        {getLinePrefix()}
      </span>
      <span className="ml-2">{line.content}</span>
      {line.lineNumber && (
        <span className="float-right text-xs opacity-60">
          {line.lineNumber.old && `${line.lineNumber.old}`}
          {line.lineNumber.old && line.lineNumber.new && '/'}
          {line.lineNumber.new && `${line.lineNumber.new}`}
        </span>
      )}
    </div>
  );
}

// Component to render a section diff with proper icons
function SectionDiffComponent({ section }: { section: SectionDiff }) {
  const getSectionIcon = () => {
    switch (section.type) {
      case 'outline':
        return <FileText className="w-4 h-4" />;
      case 'note':
        return <StickyNote className="w-4 h-4" />;
      case 'writing':
        return <Edit3 className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getActionColor = () => {
    switch (section.action) {
      case 'added':
        return 'text-green-600';
      case 'removed':
        return 'text-red-600';
      case 'modified':
        return 'text-blue-600';
      case 'unchanged':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const getActionText = () => {
    switch (section.action) {
      case 'added':
        return 'Added';
      case 'removed':
        return 'Removed';
      case 'modified':
        return 'Modified';
      case 'unchanged':
        return 'Unchanged';
      default:
        return 'Changed';
    }
  };

  return (
    <div className="border rounded bg-card">
      <div className="p-3 border-b bg-muted/50">
        <div className="flex items-center gap-2">
          {getSectionIcon()}
          <span className="font-medium">{section.title}</span>
          <Badge variant="outline" className={`ml-auto ${getActionColor()}`}>
            {getActionText()}
          </Badge>
        </div>
        {(section.addedLines > 0 || section.removedLines > 0) && (
          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
            {section.addedLines > 0 && (
              <span className="flex items-center gap-1 text-green-600">
                <Plus className="w-3 h-3" />
                {section.addedLines} added
              </span>
            )}
            {section.removedLines > 0 && (
              <span className="flex items-center gap-1 text-red-600">
                <Minus className="w-3 h-3" />
                {section.removedLines} removed
              </span>
            )}
          </div>
        )}
      </div>
      <div className="max-h-64 overflow-y-auto">
        {section.lines.map((line, index) => (
          <DiffLineComponent key={index} line={line} />
        ))}
      </div>
    </div>
  );
}

interface DocumentHistoryModalProps {
  documentId: string;
  isPremiumUser: boolean;
  showTrigger?: boolean;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
}

// Compute the diff between two document versions using the new comprehensive diff utility
function computeDiff(currentVersion: DocumentVersion, previousVersion?: DocumentVersion): DiffSummary {
  console.log("Computing diff between versions:", {
    current: currentVersion.id,
    previous: previousVersion?.id,
    currentHasContent: !!currentVersion.content,
    previousHasContent: previousVersion?.content ? true : false
  });

  // Ensure there's always some content to show
  if (!previousVersion || !currentVersion.content) {
    console.log("Missing previous version or current content, showing basic diff");
    // Return a basic diff showing this is a new version
    return {
      addedLines: 1,
      removedLines: 0,
      sections: [{
        type: 'note',
        id: 'version-info',
        title: 'Version Information',
        action: 'added',
        lines: [
          {
            type: 'added',
            content: `Version ${currentVersion.versionNumber} created by ${currentVersion.username}`
          },
          {
            type: 'added',
            content: format(new Date(currentVersion.createdAt), "MMM d, yyyy h:mm a")
          }
        ],
        addedLines: 2,
        removedLines: 0
      }]
    };
  }

  // Handle missing content in previous version
  if (!previousVersion.content) {
    console.log("Previous version missing content, showing basic comparison");
    return {
      addedLines: 1,
      removedLines: 0,
      sections: [{
        type: 'note',
        id: 'content-added',
        title: 'Content Changes',
        action: 'added',
        lines: [
          {
            type: 'added',
            content: 'Changes made to document structure'
          },
          {
            type: 'added',
            content: 'New content elements added'
          }
        ],
        addedLines: 2,
        removedLines: 0
      }]
    };
  }

  try {
    // Use the new comprehensive diff utility
    const diff = computeDocumentDiff(currentVersion.content, previousVersion.content);

    // If no changes were detected, provide a fallback message
    if (diff.sections.length === 0) {
      return {
        addedLines: 1,
        removedLines: 0,
        sections: [{
          type: 'note',
          id: 'minor-changes',
          title: 'Minor Changes',
          action: 'modified',
          lines: [
            {
              type: 'added',
              content: 'Minor formatting or metadata changes were made'
            },
            {
              type: 'unchanged',
              content: 'Document was saved without significant content changes'
            }
          ],
          addedLines: 1,
          removedLines: 0
        }]
      };
    }

    return diff;
  } catch (error) {
    console.error("Error computing diff:", error);
    // Return a fallback diff if there's an error
    return {
      addedLines: 1,
      removedLines: 1,
      sections: [{
        type: 'note',
        id: 'error',
        title: 'Error in Diff Computation',
        action: 'modified',
        lines: [
          {
            type: 'removed',
            content: 'Error processing version data'
          },
          {
            type: 'added',
            content: `Changes were made in version ${currentVersion.versionNumber}`
          }
        ],
        addedLines: 1,
        removedLines: 1
      }]
    };
  }
}

export function DocumentHistoryModal({ 
  documentId, 
  isPremiumUser,
  showTrigger = true,
  isOpen: controlledIsOpen,
  onOpenChange 
}: DocumentHistoryModalProps) {
  const [uncontrolledIsOpen, setUncontrolledIsOpen] = useState(false);
  const [expandedVersions, setExpandedVersions] = useState<Set<number>>(new Set());
  const [versionDiffs, setVersionDiffs] = useState<Record<number, DiffSummary>>({});
  const [restoreModalOpen, setRestoreModalOpen] = useState(false);
  const [versionToRestore, setVersionToRestore] = useState<number | null>(null);
  const { toast } = useToast();
  
  // Use controlled state if provided, otherwise use uncontrolled
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : uncontrolledIsOpen;
  const setIsOpen = (open: boolean) => {
    if (onOpenChange) {
      onOpenChange(open);
    } else {
      setUncontrolledIsOpen(open);
    }
  };
  
  // Fetch document versions
  const { 
    data: versions,
    isLoading,
    error,
    isError
  } = useQuery<DocumentVersion[]>({
    queryKey: ['/api/documents', documentId, 'versions'],
    queryFn: async () => {
      try {
        console.log(`Fetching versions for document ${documentId}...`);
        const response = await apiRequest('GET', `/api/documents/${documentId}/versions`);
        const data = await response.json();
        
        if (Array.isArray(data) && data.length > 0) {
          console.log(`Successfully retrieved ${data.length} versions`);
          // Log the first version to see if it contains the metrics
          console.log("First version data:", JSON.stringify(data[0], null, 2));
        } else {
          console.log("No versions found or empty array returned");
        }
        
        return data;
      } catch (error) {
        console.error("Error fetching document versions:", error);
        throw error;
      }
    },
    enabled: isOpen && isPremiumUser,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    retry: 3,
    staleTime: 5 * 60 * 1000, // 5 minutes - allow refetching after this time
    gcTime: 30 * 60 * 1000 // 30 minutes - keep in cache for this long
  });
  
  // Get version content when expanding
  const fetchVersionContentMutation = useMutation({
    mutationFn: async (versionId: number) => {
      console.log(`Fetching content for version ${versionId}`);
      
      try {
        const response = await apiRequest(
          'GET', 
          `/api/documents/${documentId}/versions/${versionId}`
        );
        
        // Check if the response is okay
        if (!response.ok) {
          // Get response status for more information
          const statusText = response.statusText;
          console.error(`Error fetching version ${versionId}: ${response.status} ${statusText}`);
          // Create a more detailed error
          throw new Error(`Could not get version details: ${response.status} ${statusText}`);
        }
        
        const data = await response.json();
        console.log(`Response received for version ${versionId}:`, data);
        return data;
      } catch (error) {
        console.error(`Error in fetchVersionContentMutation for version ${versionId}:`, error);
        throw error;
      }
    },
    
    onSuccess: (data, versionId) => {
      console.log(`Successfully loaded content for version ${versionId}:`, data);
      
      // Create a fallback content if data is missing
      if (!data.content && data.id) {
        console.log("No content in response, creating basic content object");
        data.content = {
          notes: [],
          outline: []
        };
      }
      
      // Check if versions array exists
      if (!versions || versions.length === 0) {
        console.error("No versions array available");
        return;
      }
      
      // Find the current version in our list
      const versionIndex = versions.findIndex(v => v.id === versionId);
      if (versionIndex === -1) {
        console.error(`Version ${versionId} not found in versions list`);
        return;
      }
      
      // Create a copy of the current version with the content data
      const updatedVersion = { 
        ...versions[versionIndex], 
        content: data.content || { notes: [], outline: [] }
      };
      
      console.log(`Updated version with content:`, updatedVersion);
      
      // Update the versions array with the updated version
      const updatedVersions = [...versions];
      updatedVersions[versionIndex] = updatedVersion;
      
      // Update query data to ensure the content is available for other components
      queryClient.setQueryData(['/api/documents', documentId, 'versions'], updatedVersions);
      
      // Find the previous version for comparison (next in the array chronologically)
      const previousVersionIndex = versionIndex + 1;
      const previousVersion = previousVersionIndex < updatedVersions.length 
        ? updatedVersions[previousVersionIndex] 
        : undefined;
      
      if (!previousVersion) {
        // This is the oldest version, nothing to compare with
        console.log("This is the oldest version, no comparison needed");
        setVersionDiffs(prev => ({ 
          ...prev, 
          [versionId]: { 
            addedLines: 0, 
            removedLines: 0, 
            changedSections: [{
              type: 'note',
              id: 'first-version',
              title: 'First Version',
              changes: ['This is the initial version']
            }]
          } 
        }));
        return;
      }
      
      // Check if previous version already has content
      if (previousVersion.content) {
        console.log("Computing diff with existing previous version content");
        const diff = computeDiff(updatedVersion, previousVersion);
        console.log("Diff computed:", diff);
        setVersionDiffs(prev => ({ ...prev, [versionId]: diff }));
      } else {
        // Fetch previous version content for comparison
        console.log(`Need to fetch previous version ${previousVersion.id} content`);
        
        // Create a temporary diff until we get the previous version
        setVersionDiffs(prev => ({ 
          ...prev, 
          [versionId]: { 
            addedLines: 1, 
            removedLines: 0, 
            changedSections: [{ 
              type: 'note', 
              id: 'temp-diff', 
              title: 'Changes in this version', 
              changes: [
                '+ Content was updated in this version',
                '~ Loading details...'
              ] 
            }] 
          } 
        }));
        
        // Fetch the previous version content
        fetchVersionContentMutation.mutate(previousVersion.id);
      }
    },
    
    onError: (error) => {
      console.error("Failed to fetch version content:", error);
      toast({
        title: "Failed to load version details",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
      
      // Add a placeholder diff so the UI doesn't stay in loading state forever
      if (error instanceof Error) {
        const errorId = error.message.includes('versionId') 
          ? parseInt(error.message.split(' ').pop() || '0') 
          : 0;
          
        setVersionDiffs(prev => ({
          ...prev,
          [errorId]: {
            addedLines: 0,
            removedLines: 0,
            changedSections: [{
              type: 'note',
              id: 'error',
              title: 'Error Loading Data',
              changes: [
                '- Could not load version details',
                `+ ${error.message}`
              ]
            }]
          }
        }));
      }
    },
  });

  // Restore document version mutation
  const restoreVersionMutation = useMutation({
    mutationFn: async (versionId: number) => {
      const response = await apiRequest(
        'POST', 
        `/api/documents/${documentId}/restore/${versionId}`
      );
      return response.json();
    },
    onSuccess: () => {
      // Invalidate both content and versions cache since restoration creates a new backup version
      queryClient.invalidateQueries({ queryKey: ['/api/documents', documentId, 'content'] });
      queryClient.invalidateQueries({ queryKey: ['/api/documents', documentId, 'versions'] });

      toast({
        title: "Version restored",
        description: "The document has been restored to the selected version.",
        variant: "default",
      });

      setIsOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to restore version",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  const handleRestoreVersion = (versionId: number) => {
    setVersionToRestore(versionId);
    setRestoreModalOpen(true);
  };

  const confirmRestore = () => {
    if (versionToRestore !== null) {
      restoreVersionMutation.mutate(versionToRestore);
    }
    setRestoreModalOpen(false);
  };
  
  // Pre-fetch content for all versions and calculate diffs when versions are loaded
  useEffect(() => {
    if (versions && versions.length > 0 && isOpen) {
      console.log("Modal opened with versions, pre-fetching content...");
      
      // Only pre-fetch first 10 versions to avoid overwhelming the server
      const versionsToFetch = versions.slice(0, 10);
      
      // Fetch content for each version sequentially, but only if needed
      versionsToFetch.forEach((version, index) => {
        // Skip versions with pre-calculated changedSections
        if (version.changedSections && version.changedSections.length > 0) {
          console.log(`Skipping pre-fetch for version ${version.id} - already has pre-calculated metrics`);
          return;
        }
        
        // Skip versions that already have content
        if (!version.content) {
          // Use a timeout to stagger requests
          setTimeout(() => {
            if (isOpen) { // Only fetch if still open
              console.log(`Pre-fetching content for version ${version.id} (${index + 1}/${versionsToFetch.length})`);
              fetchVersionContentMutation.mutate(version.id);
            }
          }, index * 300); // Stagger requests by 300ms
        }
      });
    }
  }, [versions, isOpen]);
  
  // Toggle version expansion
  const toggleVersionExpansion = (versionId: number) => {
    console.log(`Toggling expansion for version ${versionId}`);
    setExpandedVersions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(versionId)) {
        console.log(`Closing version ${versionId}`);
        newSet.delete(versionId);
      } else {
        console.log(`Expanding version ${versionId}`);
        newSet.add(versionId);
        const version = versions?.find(v => v.id === versionId);
        console.log(`Version found:`, version);
        
        // Check if we have pre-calculated changedSections
        if (version?.changedSections && version.changedSections.length > 0) {
          console.log(`Using pre-calculated changed sections for version ${versionId}`);
          // No need to fetch content, we already have pre-calculated metrics
        } 
        // Check if we have a locally computed diff
        else if (versionDiffs[versionId]) {
          console.log(`Using locally computed diff for version ${versionId}`);
          // No need to fetch content, we already have computed metrics
        }
        // Otherwise, fetch content to compute diff
        else if (version && !version.content) {
          console.log(`Fetching content for version ${versionId}`);
          fetchVersionContentMutation.mutate(versionId);
        }
      }
      return newSet;
    });
  };

  // Render diff summary
  const renderDiffSummary = (version: DocumentVersion) => {
    // Use pre-calculated metrics from the database if available
    if (version.addedLines !== undefined || version.removedLines !== undefined) {
      // If there's no changes recorded but we have some sections,
      // show a generic "Changes" badge
      if ((version.addedLines === 0 && version.removedLines === 0) && 
          (version.changedSections && version.changedSections.length > 0)) {
        return (
          <div className="flex items-center gap-2 mt-2 text-xs">
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 flex items-center gap-1">
              <History className="h-3 w-3 mr-1" />
              Changes
            </Badge>
          </div>
        );
      }
      
      return (
        <div className="flex items-center gap-2 mt-2 text-xs">
          {version.addedLines !== undefined && version.addedLines > 0 && (
            <Badge variant="outline" className="bg-[hsl(var(--icon-green-hsl))]/10 text-icon-green border-[hsl(var(--icon-green-hsl))]/20 flex items-center gap-1">
              <Plus className="h-3 w-3" />
              {version.addedLines || 0}
            </Badge>
          )}
          {version.removedLines !== undefined && version.removedLines > 0 && (
            <Badge variant="outline" className="bg-destructive/10 text-destructive border-destructive/20 flex items-center gap-1">
              <Minus className="h-3 w-3" />
              {version.removedLines || 0}
            </Badge>
          )}
        </div>
      );
    }
    
    // Fall back to computed diffs if available (for backward compatibility)
    if (versionDiffs[version.id]) {
      const diff = versionDiffs[version.id];
      
      // If there's no changes recorded but this isn't the oldest version,
      // show a generic "Changes" badge
      if (diff.addedLines === 0 && diff.removedLines === 0 && diff.sections.length > 0) {
        return (
          <div className="flex items-center gap-2 mt-2 text-xs">
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 flex items-center gap-1">
              <History className="h-3 w-3 mr-1" />
              Changes
            </Badge>
          </div>
        );
      }
      
      return (
        <div className="flex items-center gap-2 mt-2 text-xs">
          {diff.addedLines > 0 && (
            <Badge variant="outline" className="bg-[hsl(var(--icon-green-hsl))]/10 text-icon-green border-[hsl(var(--icon-green-hsl))]/20 flex items-center gap-1">
              <Plus className="h-3 w-3" />
              {diff.addedLines}
            </Badge>
          )}
          {diff.removedLines > 0 && (
            <Badge variant="outline" className="bg-destructive/10 text-destructive border-destructive/20 flex items-center gap-1">
              <Minus className="h-3 w-3" />
              {diff.removedLines}
            </Badge>
          )}
        </div>
      );
    }
    
    // For versions without any metrics, check if this is the oldest version
    const versionIndex = versions?.findIndex(v => v.id === version.id) || 0;
    const isOldestVersion = versions && versionIndex === versions.length - 1;
    
    // Don't show anything for the oldest version if there's no metrics
    if (isOldestVersion) {
      return null;
    }
    
    // For other versions without metrics, show a loading indicator
    return (
      <div className="flex items-center gap-2 mt-2 text-xs">
        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 flex items-center gap-1">
          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
          Loading
        </Badge>
      </div>
    );
  };

  // Render premium lock screen
  const renderPremiumLock = () => (
    <div className="flex flex-col items-center justify-center p-8 gap-4 text-center">
      <Lock className="h-12 w-12 text-muted-foreground" />
      <h3 className="text-lg font-medium">Document History is a Premium Feature</h3>
      <p className="text-sm text-muted-foreground max-w-md">
        Upgrade to a premium account to access document history tracking and restore previous versions of your documents.
      </p>
      <Button>Upgrade to Premium</Button>
    </div>
  );

  // Render loading state
  const renderLoading = () => (
    <div className="space-y-3 p-4">
      <Skeleton className="h-5 w-full" />
      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-16 w-full" />
    </div>
  );

  // Render error state
  const renderError = () => (
    <div className="flex flex-col items-center justify-center p-8 gap-4 text-center">
      <AlertCircle className="h-12 w-12 text-destructive" />
      <h3 className="text-lg font-medium">Failed to load document history</h3>
      <p className="text-sm text-muted-foreground max-w-md">
        {error instanceof Error ? error.message : "An unknown error occurred."}
      </p>
      <Button onClick={() => queryClient.invalidateQueries({ queryKey: ['/api/documents', documentId, 'versions'] })}>
        Retry
      </Button>
    </div>
  );

  // Render empty state
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center p-8 gap-4 text-center">
      <History className="h-12 w-12 text-muted-foreground" />
      <h3 className="text-lg font-medium">No Version History Yet</h3>
      <p className="text-sm text-muted-foreground max-w-md">
        Your document's version history will appear here. Versions are automatically created as you work.
      </p>
      <p className="text-xs text-primary">
        Make changes to your document to generate version history.
      </p>
    </div>
  );

  // Render version list
  const renderVersionList = () => {
    if (!versions || versions.length === 0) {
      return renderEmptyState();
    }
    
    return (
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-grow">
          <div className="space-y-4 p-4">
            {versions.map((version) => (
              <Accordion
                key={version.id}
                type="single"
                collapsible
                value={expandedVersions.has(version.id) ? version.id.toString() : undefined}
                onValueChange={(value) => toggleVersionExpansion(version.id)}
                className="border rounded-md overflow-hidden"
              >
                <AccordionItem value={version.id.toString()} className="border-0">
                  <div className="p-4 hover:bg-accent/50">
                    <div className="flex justify-between items-start">
                      <div>
                        <AccordionTrigger className="p-0 hover:no-underline flex-row-reverse justify-end">
                          <p className="font-medium flex items-center gap-2 text-left">
                            <span>Version {version.versionNumber}</span>
                          </p>
                        </AccordionTrigger>
                        <p className="text-sm text-foreground">
                          {version.changeDescription || "No description"}
                        </p>
                        <div className="mt-1 flex items-center gap-2 text-xs text-muted-foreground">
                          <span>By {version.username}</span>
                          <span>•</span>
                          <span>{format(new Date(version.createdAt), "MMM d, yyyy h:mm a")}</span>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRestoreVersion(version.id);
                          }}
                          disabled={restoreVersionMutation.isPending}
                        >
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Restore
                        </Button>
                        {renderDiffSummary(version)}
                      </div>
                    </div>
                  </div>
                  <AccordionContent>
                    <div className="p-4 pt-0 border-t mt-2 bg-muted">
                      {expandedVersions.has(version.id) && !version.changedSections && !versionDiffs[version.id] ? (
                        <div className="py-4 flex justify-center">
                          <div className="animate-spin w-5 h-5 border-2 border-primary border-t-transparent rounded-full" />
                        </div>
                      ) : (
                        <div className="space-y-2 text-sm">
                          {/* Use pre-calculated sections first if available */}
                          {version.changedSections && version.changedSections.map((section, i) => (
                            <div key={i} className="border rounded bg-card p-2">
                              <div className="font-medium mb-1">
                                {section.type === 'outline' ? 'Outline' : 'Note'}: {section.title}
                              </div>
                              <div className="font-mono text-xs whitespace-pre-wrap">
                                {section.changes.map((change, j) => (
                                  <div 
                                    key={j} 
                                    className={`${
                                      change.startsWith('+') 
                                      ? 'text-icon-green bg-[hsl(var(--icon-green-hsl))]/10 border-l-2 border-[hsl(var(--icon-green-hsl))]' 
                                      : change.startsWith('-') 
                                        ? 'text-destructive bg-destructive/10 border-l-2 border-destructive' 
                                        : change.startsWith('~')
                                          ? 'text-primary bg-primary/10 border-l-2 border-primary'
                                          : 'text-muted-foreground'
                                    } p-1 pl-2`}
                                  >
                                    {change}
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}

                          {/* Use new diff format if available */}
                          {!version.changedSections && versionDiffs[version.id]?.sections?.map((section, i) => (
                            <SectionDiffComponent key={i} section={section} />
                          ))}

                          {/* Fall back to computed sections if available for backward compatibility */}
                          {!version.changedSections && versionDiffs[version.id]?.changedSections?.map((section, i) => (
                            <div key={i} className="border rounded bg-card p-2">
                              <div className="font-medium mb-1">
                                {section.type === 'outline' ? 'Outline' : 'Note'}: {section.title}
                              </div>
                              <div className="font-mono text-xs whitespace-pre-wrap">
                                {section.changes.map((change, j) => (
                                  <div
                                    key={j}
                                    className={`${
                                      change.startsWith('+')
                                      ? 'text-icon-green bg-[hsl(var(--icon-green-hsl))]/10 border-l-2 border-[hsl(var(--icon-green-hsl))]'
                                      : change.startsWith('-')
                                        ? 'text-destructive bg-destructive/10 border-l-2 border-destructive'
                                        : change.startsWith('~')
                                          ? 'text-primary bg-primary/10 border-l-2 border-primary'
                                          : 'text-muted-foreground'
                                    } p-1 pl-2`}
                                  >
                                    {change}
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}

                          {/* Show empty state if no sections are available */}
                          {(!version.changedSections || version.changedSections.length === 0) &&
                           (!versionDiffs[version.id] ||
                            (versionDiffs[version.id].sections?.length === 0 && versionDiffs[version.id].changedSections?.length === 0)) && (
                            <div className="text-center text-muted-foreground py-2">
                              This is the first version or no changes detected.
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
          </div>
        </ScrollArea>
        <DialogFooter className="px-6 py-4 border-t">
          <p className="text-xs text-muted-foreground italic">
            Document versions are automatically created as you make changes
          </p>
        </DialogFooter>
      </div>
    );
  };

  // Main render function
  const renderContent = () => {
    if (!isPremiumUser) return renderPremiumLock();
    if (isLoading) return renderLoading();
    if (isError) return renderError();
    return renderVersionList();
  };

  // Dialog with trigger
  if (showTrigger) {
    return (
      <>
        <DialogTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-2"
            onClick={() => setIsOpen(true)}
          >
            <History className="h-4 w-4" />
            Document History
          </Button>
        </DialogTrigger>
        
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-2xl h-[70vh] flex flex-col">
            <DialogHeader>
              <div className="flex items-center justify-between">
                <div>
                  <DialogTitle>Document History</DialogTitle>
                  <DialogDescription>
                    View and restore previous versions of this document
                  </DialogDescription>
                </div>
                {isPremiumUser && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      console.log("Manual refresh triggered");
                      queryClient.invalidateQueries({ queryKey: ['/api/documents', documentId, 'versions'] });
                    }}
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                )}
              </div>
            </DialogHeader>
            
            {renderContent()}
          </DialogContent>
        </Dialog>
        
        <ConfirmModal
          isOpen={restoreModalOpen}
          onClose={() => setRestoreModalOpen(false)}
          onConfirm={confirmRestore}
          title="Restore Document Version"
          description="Are you sure you want to restore this version? Your current changes will be saved as a new version."
          confirmText="Restore Version"
          cancelText="Cancel"
          variant="default"
        />
      </>
    );
  }
  
  // Content only (no dialog wrapper)
  return (
    <>
      {renderContent()}
      
      <ConfirmModal
        isOpen={restoreModalOpen}
        onClose={() => setRestoreModalOpen(false)}
        onConfirm={confirmRestore}
        title="Restore Document Version"
        description="Are you sure you want to restore this version? Your current changes will be saved as a new version."
        confirmText="Restore Version"
        cancelText="Cancel"
        variant="default"
      />
    </>
  );
}