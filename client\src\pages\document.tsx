import React, { useState, useEffect, useRef, useCallback } from 'react'; // Added useCallback
import { useParams, useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { nanoid } from 'nanoid'; // nanoid was not used here but crypto.randomUUID was, keeping for consistency if needed elsewhere
import { AppHeader } from '@/components/layout/app-header';
import { DocumentTitle } from '@/components/layout/document-title';
import { PanelManager } from '@/components/layout/panel-manager';
import { Button } from '@/components/ui/button';
import { ShareModal } from '@/components/share-modal';
import { DocumentHistoryModal } from '@/components/document-history-modal';
import { ReferenceManagerModal } from '@/components/reference';
import { OutlineImportModal } from '@/components/outline/outline-import-modal';
import { SubscriptionModal } from '@/components/subscription-modal';
import { useDocument } from '@/hooks/use-document';
import { calculateOutlineNumbers } from '@/lib/utils/outline';
import { Loader2 } from 'lucide-react';
// UserPresence and CollaborationStatus components would need to be refactored
// import { CollaborationStatus } from '@/components/collaboration/collaboration-status';
// import { UserPresence } from '@/components/collaboration/user-presence';
import { UserPresenceDisplay } from '@/components/collaboration/UserPresenceDisplay'; // Added import
import { OutlineItem, Note, UserPresenceInfo } from '@/lib/types'; // Assuming UserPresenceInfo is in lib/types or shared
import { cn } from '@/lib/utils'; // Added for conditional class names
import { ExportButton } from '@/components/writing/export-button';
import { MetadataEditor, DocumentFormat as MetadataDocumentFormat } from '@/components/metadata/MetadataEditor';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { Settings2, MessageCircle } from 'lucide-react'; // Added MessageCircle
import { DocumentChatPanel } from '@/components/chat/DocumentChatPanel';
import { DraggableChatButton } from '@/components/chat/DraggableChatButton'; // Import DraggableChatButton

export default function DocumentPage() {
  const params = useParams();
  const documentId = params?.id;
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isReferencesModalOpen, setIsReferencesModalOpen] = useState(false);
  const [isMetadataEditorOpen, setIsMetadataEditorOpen] = useState(false);
  const [notesType, setNotesType] = useState<'footnotes' | 'endnotes'>('footnotes');
  const [isChatOpen, setIsChatOpen] = useState(false); // State for chat panel visibility
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false); // State for new message indicator
  
  interface ChatButtonPosition {
    x: number;
    y: number;
  }

  const BUTTON_SIZE = 56; // h-14 w-14 -> 56px
  const BUTTON_MARGIN = 24; // equivalent of -6 utility class (e.g. bottom-6)

  const [chatButtonPosition, setChatButtonPosition] = useState<ChatButtonPosition>(() => {
    const savedPosition = localStorage.getItem('chatButtonPosition');
    if (savedPosition) {
      try {
        const parsed = JSON.parse(savedPosition);
        // Validate parsed position
        if (parsed && typeof parsed.x === 'number' && typeof parsed.y === 'number') {
          // Further validation to ensure it's within reasonable bounds might be good here,
          // but for now, just check type.
          return {
            x: Math.max(0, Math.min(parsed.x, window.innerWidth - BUTTON_SIZE)),
            y: Math.max(0, Math.min(parsed.y, window.innerHeight - BUTTON_SIZE))
          };
        }
      } catch (e) {
        console.error("Failed to parse chatButtonPosition from localStorage", e);
      }
    }
    // Default to bottom-right
    return {
      x: window.innerWidth - BUTTON_SIZE - BUTTON_MARGIN,
      y: window.innerHeight - BUTTON_SIZE - BUTTON_MARGIN
    };
  });

  // Effect to save button position to localStorage
  useEffect(() => {
    localStorage.setItem('chatButtonPosition', JSON.stringify(chatButtonPosition));
  }, [chatButtonPosition]);

  // Effect to update button position on window resize to keep it within bounds
  useEffect(() => {
    const handleResize = () => {
      setChatButtonPosition(prevPos => ({
        x: Math.max(0, Math.min(prevPos.x, window.innerWidth - BUTTON_SIZE)),
        y: Math.max(0, Math.min(prevPos.y, window.innerHeight - BUTTON_SIZE)),
      }));
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []); // Empty array means this effect runs once on mount and cleanup on unmount

  const [isDragging, setIsDragging] = useState(false);
  const dragOffsetRef = useRef<{ x: number, y: number }>({ x: 0, y: 0 });
  const movedDuringDragRef = useRef(false);
  const mouseDownPointRef = useRef<{ x: number, y: number } | null>(null);

  // Log chatButtonPosition changes for debugging
  useEffect(() => {
    console.log('DocumentPage: chatButtonPosition changed to:', chatButtonPosition);
  }, [chatButtonPosition]);

  // Effect to prevent default dragover behavior on the window, allowing for drop
  // This is for HTML5 D&D API, may not be needed for mousedown/mousemove/mouseup approach,
  // but doesn't hurt to keep for now or remove if confirmed not needed.
  useEffect(() => {
    const handleDragOver = (event: DragEvent) => {
      event.preventDefault();
    };

    window.addEventListener('dragover', handleDragOver);

    return () => {
      window.removeEventListener('dragover', handleDragOver);
    };
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    movedDuringDragRef.current = false; // Reset drag movement flag
    mouseDownPointRef.current = { x: e.clientX, y: e.clientY }; // Store initial mouse position

    setIsDragging(true);
    dragOffsetRef.current = {
      x: e.clientX - chatButtonPosition.x,
      y: e.clientY - chatButtonPosition.y,
    };
    // Prevent text selection during drag
    e.preventDefault();
  }, [chatButtonPosition.x, chatButtonPosition.y]); // Dependencies are correct

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      if (!movedDuringDragRef.current && mouseDownPointRef.current) {
        const dx = Math.abs(e.clientX - mouseDownPointRef.current.x);
        const dy = Math.abs(e.clientY - mouseDownPointRef.current.y);
        if (dx > 5 || dy > 5) { // Threshold of 5 pixels
          movedDuringDragRef.current = true;
        }
      }

      let newX = e.clientX - dragOffsetRef.current.x;
      let newY = e.clientY - dragOffsetRef.current.y;

      // Boundary checks
      newX = Math.max(0, Math.min(newX, window.innerWidth - BUTTON_SIZE));
      newY = Math.max(0, Math.min(newY, window.innerHeight - BUTTON_SIZE));

      setChatButtonPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      mouseDownPointRef.current = null; // Reset mouseDownPoint on mouse up
    };

    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, setChatButtonPosition]);
  
  const {
    title,
    setTitle,
    outline,
    setOutline,
    notes,
    createNote,
    updateNote,
    deleteNote,
    activeOutlineItemId,
    setActiveOutlineItemId,
    saveStatus,
    saveDocument, // Manual save trigger
    uploadFile,
    getCurrentWriting,
    updateWritingContent,
    addCitation,
    updateCitation,
    deleteCitation,
    getWordCount,
    checkText,
    isLoading,
    linkNoteToOutlineItem,
    deleteOutlineItemWithNotes,
    duplicateNote,
    documentFormat,
    metadata,
    updateDocumentFormat,
    updateMetadata,
    presentUsers, // From useDocument
    chatMessages, // Added for chat
    sendChatMessage, // Added for chat
    refreshDocumentContent, // Added for popout sync
    currentUserPermissionLevel, // Added for permission-based UI
    documentOwnerId, // Added for ownership-based UI
  } = useDocument({
    initialDocumentId: documentId,
    token: new URLSearchParams(window.location.search).get('token'),
    // initialFormatProp: documentFormat, // Pass if needed for useDocument's internal logic
    onNewMessageWhileClosed: useCallback(() => {
      // This callback is called by useDocument when a new message arrives.
      // We check DocumentPage's isChatOpen state here.
      if (!isChatOpen) {
        setHasUnreadMessages(true);
      }
    }, [isChatOpen, setHasUnreadMessages]), // Dependencies: isChatOpen to get its current value,
                                          // setHasUnreadMessages (though stable, good practice)
  });

  // Check if current user is the document owner
  const isDocumentOwner = user?.id === documentOwnerId;

  useEffect(() => {
    const savedNotesType = localStorage.getItem('notesType');
    if (savedNotesType === 'footnotes' || savedNotesType === 'endnotes') {
      setNotesType(savedNotesType as 'footnotes' | 'endnotes');
    }
  }, []);

  const handleChatButtonClick = useCallback(() => {
    if (movedDuringDragRef.current) {
      // A drag operation just concluded, so don't treat this as a click.
      return;
    }
    // This is a genuine click
    const newChatOpenState = !isChatOpen;
    setIsChatOpen(newChatOpenState);
    if (newChatOpenState) { // If panel is being opened
      setHasUnreadMessages(false);
    }
  }, [isChatOpen, movedDuringDragRef, setIsChatOpen, setHasUnreadMessages]);

  // Handle clicking outside chat to close it
  const handleMainAreaClick = useCallback((e: React.MouseEvent) => {
    // Only close chat if it's open
    if (isChatOpen) {
      setIsChatOpen(false);
    }
  }, [isChatOpen]);

  // Effect for logging or showing toasts based on presence changes
  useEffect(() => {
    if (presentUsers && presentUsers.length > 0) {
      console.log("DocumentPage: Present users updated:", presentUsers.map(u => u.username).join(', '));
      // TODO: Implement more sophisticated join/leave notifications if desired,
      // e.g., by comparing previous presentUsers state with current.
    } else if (presentUsers) { // It's an empty array
      console.log("DocumentPage: Present users list is empty.");
    } else { // presentUsers is undefined or null
      console.log("DocumentPage: presentUsers is undefined/null. This might indicate an issue with useDocument hook's return value.");
    }
  }, [presentUsers, toast]);

  const handleDocuments = () => {
    navigate('/documents');
  };
  
  const handleSpellCheck = () => checkText('spelling');
  const handleGrammarCheck = () => checkText('grammar');
  
  const handleNotesTypeChange = (type: 'footnotes' | 'endnotes') => {
    setNotesType(type);
    localStorage.setItem('notesType', type);
  };
  
  const handleAddOutlineItem = (parentId?: string) => {
    const newId = crypto.randomUUID(); // Use crypto.randomUUID for IDs
    let newOutline: OutlineItem[];

    if (parentId) {
      const addRecursive = (items: OutlineItem[]): OutlineItem[] => {
        return items.map(item => {
          if (item.id === parentId) {
            return {
              ...item,
              children: [
                ...(item.children || []),
                { id: newId, number: '', title: 'New Subsection', children: [] }
              ]
            };
          }
          if (item.children) {
            return { ...item, children: addRecursive(item.children) };
          }
          return item;
        });
      };
      newOutline = addRecursive(outline);
    } else {
      newOutline = [
        ...outline,
        { id: newId, number: '', title: 'New Section', children: [] }
      ];
    }
    setOutline(calculateOutlineNumbers(newOutline));
  };
  
  const handleOutlineChange = (updatedOutline: OutlineItem[]) => {
    setOutline(calculateOutlineNumbers(updatedOutline));
  };
  
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);

  const handleImportOutline = () => setIsImportModalOpen(true);
  
  const handleImportComplete = (importedOutline: OutlineItem[]) => {
    setOutline(calculateOutlineNumbers(importedOutline));
  };

  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-16 w-16 animate-spin text-primary" />
          <p className="text-lg font-medium text-foreground">Loading document...</p>
        </div>
      </div>
    );
  }

  const handleShare = () => setIsShareModalOpen(true);
  const handleViewHistory = () => setIsHistoryModalOpen(true);
  const handleViewReferences = () => setIsReferencesModalOpen(true);

  return (
    <div className={cn(
      "font-sans text-foreground h-screen flex flex-col",
      isDocumentOwner
        ? "bg-background"
        : "bg-blue-50 dark:bg-blue-950/30"
    )}>
      <AppHeader
        showDocActions={true}
        onDocuments={handleDocuments}
        onShare={handleShare}
        onReferences={handleViewReferences}
      />
      
      <DocumentTitle
        title={title}
        onTitleChange={(newTitle) => {
            setTitle(newTitle);
            if (metadata && updateMetadata) {
                updateMetadata({ mainTitle: newTitle });
            }
        }}
        rightContent={
          <div className="flex items-center gap-2">
            {/* Only show metadata and history buttons for document owners */}
            {isDocumentOwner && (
              <>
                <Button variant="outline" size="sm" className="shadow-sm flex items-center gap-2 border-border" onClick={() => setIsMetadataEditorOpen(true)}>
                  <Settings2 className="h-4 w-4" />
                  <span>Metadata</span>
                </Button>
                <Button variant="outline" size="sm" className="shadow-sm flex items-center gap-2 border-border" onClick={handleViewHistory}>
                  <i className="ri-history-line"></i>
                  <span>History</span>
                  {user?.isPremium ? null : (
                    <span className="bg-gradient-to-br from-amber-400 to-orange-600 text-white text-xs px-1.5 py-0.5 rounded-sm">Premium</span>
                  )}
                </Button>
              </>
            )}
            {/* Only show export button for document owners */}
            {isDocumentOwner && (
              <ExportButton
                content={getCurrentWriting().content}
                outline={outline}
                notes={notes}
                citations={getCurrentWriting().citations || []}
                documentId={documentId}
                notesType={notesType}
                disabled={false}
              />
            )}
          </div>
        }
      />
      
      <main className="w-full flex-grow flex flex-col overflow-hidden" onClick={handleMainAreaClick}>
        <div className="flex flex-1 w-full overflow-hidden rounded-lg shadow-sm bg-card">
          <PanelManager
            documentId={documentId}
            outline={outline}
            notes={notes}
            activeOutlineItemId={activeOutlineItemId}
            content={getCurrentWriting().content}
            citations={getCurrentWriting().citations || []}
            onOutlineChange={handleOutlineChange}
            onAddOutlineItem={handleAddOutlineItem}
            onImportOutline={handleImportOutline}
            onNoteCreate={createNote} // Direct pass-through
            onNoteUpdate={updateNote} // Direct pass-through
            onNoteDelete={deleteNote} // Direct pass-through
            onFileUpload={uploadFile}
            onOutlineItemSelect={setActiveOutlineItemId}
            onContentChange={updateWritingContent} // Direct pass-through
            onAddCitation={addCitation} // Direct pass-through
            onUpdateCitation={updateCitation} // Direct pass-through
            onDeleteCitation={deleteCitation} // Direct pass-through
            onSpellCheck={handleSpellCheck}
            onGrammarCheck={handleGrammarCheck}
            notesType={notesType}
            onNotesTypeChange={handleNotesTypeChange}
            onLinkNoteToOutlineItem={linkNoteToOutlineItem}
            onDeleteOutlineItemWithNotes={deleteOutlineItemWithNotes}
            onNoteDuplicate={duplicateNote}
            onRefreshContent={refreshDocumentContent}
            getCurrentWriting={getCurrentWriting}
            currentUserPermissionLevel={currentUserPermissionLevel}
            isDocumentOwner={isDocumentOwner}
          />
          {/* The DocumentChatPanel will be moved out of this flex container to float independently */}
        </div>
      </main>

      {/* Floating Document Chat Panel */}
      {documentId && sendChatMessage && user?.id != null && (
        <div
          className={cn(
            "fixed top-0 bottom-0 h-full bg-card shadow-xl transition-transform duration-300 ease-in-out z-40 flex flex-col",
            (chatButtonPosition.x < window.innerWidth / 2) ? "left-0 border-r border-border" : "right-0 border-l border-border",
            isChatOpen ? "translate-x-0" : ((chatButtonPosition.x < window.innerWidth / 2) ? "-translate-x-full" : "translate-x-full")
          )}
          style={{ width: '350px' }}
          onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside chat panel
        >
          {isChatOpen && (
            <DocumentChatPanel
              chatMessages={chatMessages}
              sendChatMessage={sendChatMessage}
              currentUserId={user.id}
            />
          )}
        </div>
      )}
      
      <div className="flex items-center justify-between py-1 px-3 border-t border-border bg-background text-foreground">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1">
            {saveStatus === 'saved' && (<><i className="ri-check-line text-success-500"></i><span>Saved</span></>)}
            {saveStatus === 'saving' && (<><i className="ri-loader-2-line animate-spin text-primary-500"></i><span>Saving...</span></>)}
            {saveStatus === 'unsaved' && (<><i className="ri-error-warning-line text-warning-500"></i><span>Unsaved changes</span></>)}
          </div>
          <div>Word count: {getWordCount()}</div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Placeholder for UserPresence and CollaborationStatus - to be refactored with presentUsers */}
          <UserPresenceDisplay presentUsers={presentUsers} currentUserId={user?.id} />
          <div className="flex items-center gap-4 ml-4">
            <Button variant="ghost" size="sm" className="flex items-center gap-1 text-muted-foreground hover:text-primary" onClick={() => {}}>
              <i className="ri-error-warning-line"></i>
              <span>Report Issue</span>
            </Button>
            <Button variant="ghost" size="sm" className="flex items-center gap-1 text-muted-foreground hover:text-primary" onClick={() => {}}>
              <i className="ri-question-line"></i>
              <span>Help</span>
            </Button>
          </div>
        </div>
      </div>
      
      <ShareModal isOpen={isShareModalOpen} onClose={() => setIsShareModalOpen(false)} documentId={documentId || ""} documentTitle={title} />
      
      <Dialog open={isHistoryModalOpen} onOpenChange={setIsHistoryModalOpen}>
        <DialogContent className="max-w-2xl h-[70vh] flex flex-col">
          <DialogHeader><DialogTitle>Document History</DialogTitle><DialogDescription>View and restore previous versions of this document</DialogDescription></DialogHeader>
          <DocumentHistoryModal documentId={documentId || ""} isPremiumUser={user?.isPremium || false} showTrigger={false} isOpen={isHistoryModalOpen} onOpenChange={setIsHistoryModalOpen} />
        </DialogContent>
      </Dialog>
      
      <ReferenceManagerModal
        allReferences={getCurrentWriting().citations || []}
        onAddReference={(newReference) => {
          if (!activeOutlineItemId) {
            toast({ title: "Cannot add reference", description: "Please select a document section first", variant: "destructive" });
            return;
          }
          addCitation(newReference); // addCitation now handles saving
          toast({ title: "Reference Added", description: `Added "${newReference.title}" to your document` });
        }}
        onUpdateReference={updateCitation} // updateCitation now handles saving
        onDeleteReference={deleteCitation} // deleteCitation now handles saving
        showTrigger={false}
        isOpen={isReferencesModalOpen}
        onOpenChange={setIsReferencesModalOpen}
      />

      <OutlineImportModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onImport={handleImportComplete}
        currentOutline={outline}
        isPremiumUser={user?.isPremium || false}
      />

      <Dialog open={isMetadataEditorOpen} onOpenChange={setIsMetadataEditorOpen}>
        <DialogContent className="sm:max-w-2xl md:max-w-3xl max-h-[80vh] flex flex-col">
          <DialogHeader><DialogTitle>Document Metadata</DialogTitle></DialogHeader>
          <div className="flex flex-col flex-grow min-h-0">
            {documentFormat && metadata && updateDocumentFormat && updateMetadata ? (
              <MetadataEditor documentFormat={documentFormat as MetadataDocumentFormat} metadata={metadata} onFormatChange={updateDocumentFormat} onMetadataChange={updateMetadata} />
            ) : (
              <div className="text-center p-8"><p>Loading metadata...</p>{(isLoading || !documentFormat || !metadata) && <Loader2 className="mx-auto mt-2 h-8 w-8 animate-spin text-primary" />}</div>
            )}
          </div>
          <DialogFooter className="sm:justify-end"><DialogClose asChild><Button type="button" variant="outline">Close</Button></DialogClose></DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Chat Toggle Button */}
      <div
        className="fixed z-50 cursor-grab active:cursor-grabbing" // Added cursor styles
        style={{
          top: `${chatButtonPosition.y}px`,
          left: `${chatButtonPosition.x}px`,
        }}
        onMouseDown={handleMouseDown} // Attached onMouseDown
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking chat button
      >
        <DraggableChatButton
          onClick={handleChatButtonClick} // Use the memoized handler
          // onPositionChange={setChatButtonPosition} // This prop is removed from DraggableChatButton
          hasUnreadMessages={hasUnreadMessages}
          isChatOpen={isChatOpen}
        />
      </div>
    </div>
  );
}