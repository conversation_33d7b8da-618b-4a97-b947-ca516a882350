export interface AttachmentMetadata {
  url: string;
  id: string;
  type: string;
  name: string;
  displayName: string;
  size: number;
  mimetype: string;
  linkedNoteIds?: string[];
}

export interface Note {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  linkedOutlineId: string;
  linkedOutlineNumber?: string;
  position?: number; // For ordering notes within an outline item
  imageUrls?: string[];
  videoUrls?: string[];
  fileUrls?: string[];
  type?: 'text' | 'image' | 'video' | 'file';
  primaryAssetUrl?: string;
  // Attachment metadata
  imageUrlsData?: AttachmentMetadata[];
  videoUrlsData?: AttachmentMetadata[];
  fileUrlsData?: AttachmentMetadata[];
  primaryAssetData?: AttachmentMetadata;
}

export interface OutlineItem {
  id: string;
  title: string;
  children?: OutlineItem[];
  number?: string; // For numbered items
}

export interface DocumentContent {
  notes: Note[];
  outline: OutlineItem[];
  writing?: {
    [sectionId: string]: {
      content: string;
      citations?: Citation[];
    };
  };
}

export interface Document {
  id: number;
  userId: number;
  title: string;
}

export interface Citation {
  id: string;
  marker: string;
  reference: string;
  // Enhanced citation properties
  type?: 'book' | 'article' | 'website' | 'journal' | 'conference' | 'other';
  title?: string;
  authors?: string[];
  year?: string;
  url?: string;
  doi?: string;
  tags?: string[];
  notes?: string;
  // Source information
  publisher?: string;
  journal?: string;
  // Format preference for drag-and-drop
  isFootnote?: boolean;
  volume?: string;
  issue?: string;
  pages?: string;
  // Collection information
  collectionId?: string;
}

export interface ReferenceCollection {
  id: string;
  name: string;
  color?: string;
  description?: string;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: number;
  username: string;
  preferences?: import('../../shared/schema').UserPreferences;
}

export interface DocumentVersion {
  id: number;
  documentId: number;
  userId: number;
  versionNumber: number;
  changeDescription: string | null;
  createdAt: string;
  username: string;
  content?: DocumentContent;
}

export interface Signer {
  name: string;
  jobTitle: string;
  role: string;
}

export interface DocumentExportMetadata {
  // Author information
  firstName: string;
  middleName?: string;
  lastName: string;
  coauthors?: string;

  // Class information
  instructor?: string;
  course?: string;
  courseName?: string;
  institution?: string;
  submissionDate: string;

  // Title information
  shortTitle?: string;
  mainTitle: string;
  subtitle?: string;

  // Headings
  tocHeading?: string;
  introductionHeading?: string;
  conclusionHeading?: string;
  bibliographyHeading?: string;
  authorNote?: string;
  dedication?: string;
  abstract?: string;
  keywords?: string;
  footnotes?: string;

  // Conferment information
  degreeTitle?: string;
  college?: string;
  copyrightYear?: string;

  // Signers information
  signers?: Signer[];
}

// Granular Document Change Types (DocumentChangeType enum, DocumentChange union, etc.)
// are now directly imported from 'shared/schema' in files that need them (e.g., use-document.ts).
// Thus, re-exporting them here is removed to avoid confusion and potential issues with enum value exports.

// This is the type that was previously in useDocument.ts for the naive changes
// It can be deprecated or removed once granular changes are fully implemented.
export interface NaiveDocumentChange {
  elementId: 'outline' | 'notes' | 'writing';
  elementType: 'outlineContainer' | 'notesContainer' | 'writingContainer';
  oldValue: any;
  newValue: any;
}

// Type for the overall document content data structure (consistent with shared/schema.ts DocumentContentData)
// This can be useful for client-side representations.
// Re-defining or importing from a shared location if this file is client-only.
// For now, let's assume DocumentContentData from shared/schema.ts is the source of truth.
// We already have DocumentContent here, which is similar.
export type { DocumentContentData } from '../../../shared/schema'; // Assuming relative path