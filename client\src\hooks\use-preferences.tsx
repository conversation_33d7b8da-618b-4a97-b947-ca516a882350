import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { UserPreferences, userPreferencesSchema } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';

const defaultPreferences: UserPreferences = {
  colorScheme: "Light",
  customThemeColor: "#3b82f6", // Default blue primary color
  fontFaceLabels: "Aptos",
  fontScaleLabels: "100%",
  fontFaceUserText: "Aptos",
  fontScaleUserText: "100%",
  textToSpeechVocalPitch: "Baritone",
  textToSpeechLocale: "US Canada English",
  textToSpeechStyle: "Precise and articulate",
  subscriberNameOptions: {
    title: true,
    firstName: true,
    middleName: true,
    lastName: true,
    suffix: true,
  },
  // Paragraph formatting preferences
  paragraphIndent: "0",
  lineSpacing: "1.15",
  paragraphSpacing: "0.5em",
  visiblePanels: ['outline', 'writing'], // Default visible panels
};

type PreferencesContextType = {
  preferences: UserPreferences;
  isLoading: boolean;
  updatePreferences: (newPreferences: UserPreferences, showToast?: boolean) => void;
  // colorVariables: Record<string, string>; // This was already marked for removal
  fontScales: Record<string, string>;
  speakText: (text: string) => void;
  // Panel sizes - note that these are already part of UserPreferences, but might be useful to have them explicitly here
  // if we ever wanted to pass them down separately or perform specific logic on them within the context.
  // For now, they are accessed via preferences.outlinePanelSize, etc.
};

const PreferencesContext = createContext<PreferencesContextType | null>(null);

export const usePreferences = () => {
  const context = useContext(PreferencesContext);
  if (!context) {
    throw new Error('usePreferences must be used within a PreferencesProvider');
  }
  return context;
};

interface PreferencesProviderProps {
  children: ReactNode;
}

export const PreferencesProvider = ({ children }: PreferencesProviderProps) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [preferences, setPreferences] = useState<UserPreferences>(defaultPreferences);
  const [fontScales, setFontScales] = useState<Record<string, string>>({});
  const [speechSynthesis, setSpeechSynthesis] = useState<SpeechSynthesis | null>(null);

  // Fetch user preferences
  const { data: userPreferences, isLoading } = useQuery({
    queryKey: ['/api/user/preferences'],
    enabled: !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Handle preferences data updates
  useEffect(() => {
    if (userPreferences) {
      try {
        // Simple migration: check for and remove old 'notes' related panel preferences
        let preferencesToValidate = { ...userPreferences };
        if ('notesPanelSize' in preferencesToValidate) {
          delete (preferencesToValidate as any).notesPanelSize;
        }
        if (preferencesToValidate.visiblePanels?.includes('notes')) {
          preferencesToValidate.visiblePanels = preferencesToValidate.visiblePanels.filter(p => p !== 'notes');
        }

        const validatedPreferences = userPreferencesSchema.parse(preferencesToValidate);
        setPreferences(validatedPreferences);
      } catch (error) {
        console.error('Invalid preferences format:', error);
        setPreferences(defaultPreferences);
      }
    }
  }, [userPreferences]);

  // Handle error case - set default preferences if no user is logged in
  useEffect(() => {
    if (!user) {
      setPreferences(defaultPreferences);
    }
  }, [user]);

  type UpdatePreferencesMutationParams = {
    preferences: UserPreferences;
    showToast: boolean;
  };

  const updatePreferencesMutation = useMutation<any, Error, UpdatePreferencesMutationParams>({
    mutationFn: async (params: UpdatePreferencesMutationParams) => {
      const { preferences } = params;
      const response = await fetch('/api/user/preferences', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(preferences)
      });
      
      if (!response.ok) {
        throw new Error('Failed to update preferences');
      }
      
      return { preferences: await response.json(), showToast: params.showToast };
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/preferences'] });
      if (data.showToast) {
        toast({
          title: "Preferences updated",
          description: "Your preferences have been saved successfully."
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  useEffect(() => {
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      setSpeechSynthesis(window.speechSynthesis);
    }
  }, []);

  const hexToRgb = (hex: string): { r: number, g: number, b: number } | null => {
    hex = hex.replace(/^#/, '');
    let r, g, b;
    if (hex.length === 3) {
      r = parseInt(hex[0] + hex[0], 16);
      g = parseInt(hex[1] + hex[1], 16);
      b = parseInt(hex[2] + hex[2], 16);
    } else if (hex.length === 6) {
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    } else {
      return null;
    }
    return { r, g, b };
  };
  
  const rgbToHsl = (r: number, g: number, b: number): { h: number, s: number, l: number } => {
    r /= 255; g /= 255; b /= 255;
    const max = Math.max(r, g, b), min = Math.min(r, g, b);
    let h, s;
    const l = (max + min) / 2;
    if (max === min) {
      h = s = 0; 
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
        default: h = 0; 
      }
      h = Math.round(h * 60);
      if (h < 0) h += 360;
    }
    s = Math.round(s * 100);
    const l_value = Math.round(l * 100);
    return { h, s, l: l_value };
  };
  
  // Apply color scheme
  useEffect(() => {
    if (preferences) {
      let bodyClass = '';
      const root = document.documentElement;

      const getHslString = (hexColor: string, defaultHsl: string): string => {
        const rgb = hexToRgb(hexColor);
        if (rgb) {
          const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
          return `${hsl.h} ${hsl.s}% ${hsl.l}%`;
        }
        return defaultHsl;
      };
      
      const customPrimaryHex = preferences.customThemeColor || "#3b82f6";
      const primaryHsl = getHslString(customPrimaryHex, "214 100% 60%");
      root.style.setProperty('--primary', primaryHsl);
      root.style.setProperty('--ring', primaryHsl);

      switch (preferences.colorScheme) {
        case "Light":
          // Light theme - use default CSS variables (already defined in CSS)
          bodyClass = '';
          break;
        case "Dark":
          // Dark theme - use dark CSS variables (already defined in CSS)
          bodyClass = 'dark';
          break;
        default:
          // Fallback to light theme
          bodyClass = '';
          break;
      }
            
      if (bodyClass === 'dark') {
        document.body.classList.add('dark');
      } else {
        document.body.classList.remove('dark');
      }
    }
  }, [
      preferences?.colorScheme,
      preferences?.customThemeColor,
    ]);

  // Apply font scales
  useEffect(() => {
    if (preferences) {
      const getScaleFactor = (percentageString: string | undefined) => {
        switch (percentageString) {
          case "75%": return "0.75";
          case "90%": return "0.9";
          case "110%": return "1.1";
          case "125%": return "1.25";
          case "150%": return "1.5";
          default: return "1";
        }
      };

      const newFontScales = {
        "--font-face-labels": preferences.fontFaceLabels || "Aptos",
        "--font-face-user-text": preferences.fontFaceUserText || "Aptos",
        "--font-scale-labels": getScaleFactor(preferences.fontScaleLabels),
        "--font-scale-user-text": getScaleFactor(preferences.fontScaleUserText)
      };
      
      setFontScales(newFontScales);
      
      Object.entries(newFontScales).forEach(([key, value]) => {
        document.documentElement.style.setProperty(key, value);
      });
      
      document.documentElement.style.setProperty('--font-family-labels', `var(--font-face-labels), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`);
      document.documentElement.style.setProperty('--font-family-user-text', `var(--font-face-user-text), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`);
      document.documentElement.style.setProperty('--font-size-base-labels', 'calc(1rem * var(--font-scale-labels))');
      document.documentElement.style.setProperty('--font-size-base-user-text', 'calc(1rem * var(--font-scale-user-text))');
    }
  }, [preferences?.fontFaceLabels, preferences?.fontFaceUserText, preferences?.fontScaleLabels, preferences?.fontScaleUserText]);

  const speakText = (text: string) => {
    if (!speechSynthesis) return;
    speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    const voices = speechSynthesis.getVoices();
    let selectedVoice;
    switch (preferences.textToSpeechLocale) {
      case "UK English": selectedVoice = voices.find(voice => voice.lang === 'en-GB'); break;
      case "Australian English": selectedVoice = voices.find(voice => voice.lang === 'en-AU'); break;
      case "Indian English": selectedVoice = voices.find(voice => voice.lang === 'en-IN'); break;
      default: selectedVoice = voices.find(voice => voice.lang === 'en-US'); break;
    }
    if (selectedVoice) utterance.voice = selectedVoice;
    switch (preferences.textToSpeechVocalPitch) {
      case "Soprano": utterance.pitch = 1.5; break;
      case "Alto": utterance.pitch = 1.2; break;
      case "Tenor": utterance.pitch = 1.0; break;
      case "Bass": utterance.pitch = 0.7; break;
      default: utterance.pitch = 0.9; break;
    }
    switch (preferences.textToSpeechStyle) {
      case "Casual and relaxed": utterance.rate = 0.9; break;
      case "Energetic": utterance.rate = 1.2; break;
      case "Professional": utterance.rate = 1.0; break;
      default: utterance.rate = 0.95; break;
    }
    speechSynthesis.speak(utterance);
  };

  const updatePreferences = React.useCallback((newPreferences: UserPreferences, showToast: boolean = false) => {
    // It's generally better to update the local state (if used for immediate UI feedback)
    // based on the successful mutation, or trust react-query's cache.
    // However, the current pattern sets local state immediately.
    // This local setPreferences can cause the very re-render that changes this callback's instance
    // if not handled carefully. For now, let's stick to stabilizing the function passed to consumers.
    setPreferences(newPreferences); 
    updatePreferencesMutation.mutate({ preferences: newPreferences, showToast });
  }, [updatePreferencesMutation.mutate]); // updatePreferencesMutation.mutate is stable

  const value = {
    preferences,
    isLoading,
    updatePreferences,
    fontScales,
    speakText
  };

  return (
    <PreferencesContext.Provider value={value}>
      {children}
    </PreferencesContext.Provider>
  );
};