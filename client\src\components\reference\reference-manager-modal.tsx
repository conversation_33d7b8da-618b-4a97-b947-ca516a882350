import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Citation } from '@/lib/types';
import { ConfirmModal } from '@/components/ui/confirm-modal';
import { formatCitation, CitationStyle } from '@/lib/utils/citation-formatter';
import { parseBibTeX, parseRIS, parseTextCitation } from '@/lib/utils/citation-import';
import { nanoid } from 'nanoid';
import {
  useUserReferences,
  useAddReference,
  useUpdateReference,
  useDeleteReference,
  useReferenceCollections,
  useAddCollection,
  useUpdateCollection,
  useDeleteCollection
} from "@/hooks/use-references";
import type { Reference, ReferenceCollection } from '@shared/schema';

// Define reference types
const referenceTypes = [
  { value: 'journal', label: 'Journal' },
  { value: 'book', label: 'Book' },
  { value: 'article', label: 'Article' },
  { value: 'website', label: 'Website' },
  { value: 'conference', label: 'Conference' },
  { value: 'other', label: 'Other' }
];

// Color palette for collections - theme compatible
const COLLECTION_COLORS = [
  'bg-destructive/20 text-destructive',
  'bg-primary/20 text-primary',
  'bg-[var(--icon-green)]/20 text-icon-green',
  'bg-[var(--icon-amber)]/20 text-icon-amber',
  'bg-[var(--icon-purple)]/20 text-icon-purple',
  'bg-orange-500/20 text-orange-500 dark:text-orange-400',
  'bg-teal-500/20 text-teal-500 dark:text-teal-400',
  'bg-pink-500/20 text-pink-500 dark:text-pink-400'
];

// Interface for reference collections
export interface ReferenceCollectionType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

// Props for the reference manager modal
interface ReferenceManagerModalProps {
  allReferences: Citation[];
  onAddReference: (reference: Citation) => void;
  onUpdateReference: (reference: Citation) => void;
  onDeleteReference: (referenceId: string) => void;
  showTrigger?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

// Initial values for manual entry form
const initialReferenceFields: Partial<Reference> = {
  id: '',
  type: 'book',
  title: '',
  authors: [],
  year: '',
  journal: '',
  publisher: '',
  url: '',
  doi: '',
  accessed: '',
  pages: '',
  volume: '',
  issue: '',
};

export function ReferenceManagerModal({
  allReferences,
  onAddReference,
  onUpdateReference,
  onDeleteReference,
  showTrigger = true,
  isOpen: controlledIsOpen,
  onOpenChange: controlledOnOpenChange
}: ReferenceManagerModalProps) {
  // State for dialog modal
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const isOpen = controlledIsOpen ?? internalIsOpen;
  const onOpenChange = controlledOnOpenChange ?? setInternalIsOpen;
  
  // Toast notifications
  const { toast } = useToast();

  // Navigation and tabs
  const [activeTab, setActiveTab] = useState('library');
  const [showBackButton, setShowBackButton] = useState(false);

  // Library filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all_types');
  
  // Collection management
  const [collections, setCollections] = useState<ReferenceCollection[]>([
    { id: 'all', name: 'All References', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
    { id: 'uncategorized', name: 'Uncategorized', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
  ]);
  const [activeCollection, setActiveCollection] = useState<string>('all');
  const [showCollectionInput, setShowCollectionInput] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [deleteCollectionId, setDeleteCollectionId] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [editingCollection, setEditingCollection] = useState<ReferenceCollection | null>(null);
  const [deleteCollection, setDeleteCollection] = useState<ReferenceCollection | null>(null);
  
  // Reference creation and editing state
  const [referenceFields, setReferenceFields] = useState<Partial<Reference>>(initialReferenceFields);
  const [editingReference, setEditingReference] = useState<Reference | null>(null);
  const [editedReference, setEditedReference] = useState('');
  
  // Import state
  const [importText, setImportText] = useState('');
  const [importFormat, setImportFormat] = useState('bibtex');
  
  // ISBN lookup state
  const [isbnQuery, setIsbnQuery] = useState('');
  const [isbnLoading, setIsbnLoading] = useState(false);

  // Get global references from the server
  const {
    data: globalReferences = [],
    isLoading: isLoadingReferences,
    refetch: refetchReferences
  } = useUserReferences();

  // Get global collections from the server
  const {
    data: globalCollections = [],
    isLoading: isLoadingCollections,
    refetch: refetchCollections
  } = useReferenceCollections();

  // Mutations for references
  const addReferenceMutation = useAddReference();
  const updateReferenceMutation = useUpdateReference();
  const deleteReferenceMutation = useDeleteReference();

  // Mutations for collections
  const addCollectionMutation = useAddCollection();
  const updateCollectionMutation = useUpdateCollection();
  const deleteCollectionMutation = useDeleteCollection();

  // Update the collections list when server data changes - using a ref to prevent infinite loops
  const prevCollectionsRef = useRef<string>('');
  
  useEffect(() => {
    // Keep the built-in collections
    const baseCollections = [
      { id: 'all', name: 'All References', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
      { id: 'uncategorized', name: 'Uncategorized', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
    ];

    // Add any user-created collections from the server
    if (globalCollections && Array.isArray(globalCollections)) {
      // Create a stable string representation to compare
      const newCollectionsStr = JSON.stringify(globalCollections);

      // Only update if the collections have actually changed from the last update
      if (prevCollectionsRef.current !== newCollectionsStr) {
        prevCollectionsRef.current = newCollectionsStr;
        setCollections([
          ...baseCollections,
          ...(globalCollections as ReferenceCollection[])
        ]);
      }
    }
  }, [globalCollections]);

  // Filter references based on search and filters
  const filteredReferences = (globalReferences as Reference[]).filter(reference => {
    // Collection filter
    if (activeCollection !== 'all') {
      if (activeCollection === 'uncategorized') {
        // Show only uncategorized references
        if (reference.collectionId) {
          return false;
        }
      } else if (reference.collectionId !== activeCollection) {
        // Show only references in the selected collection
        return false;
      }
    }

    // Type filter
    if (filterType !== 'all_types' && reference.type !== filterType) {
      return false;
    }

    // Search text filter
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      const matchesTitle = reference.title?.toLowerCase()?.includes(searchLower);
      const matchesAuthors = reference.authors?.some(author =>
        author.toLowerCase().includes(searchLower)
      );
      const matchesYear = reference.year?.toLowerCase()?.includes(searchLower);
      const matchesJournal = reference.journal?.toLowerCase()?.includes(searchLower);
      const matchesPublisher = reference.publisher?.toLowerCase()?.includes(searchLower);

      return matchesTitle || matchesAuthors || matchesYear || matchesJournal || matchesPublisher;
    }

    return true;
  });

  // Reset all form fields
  const resetForm = () => {
    setReferenceFields(initialReferenceFields);
    setEditingReference(null);
    setShowBackButton(false);
  };

  // Get a color for a collection
  const getCollectionColor = (collectionId: string) => {
    if (collectionId === 'all') return 'bg-secondary/70 text-secondary-foreground';
    if (collectionId === 'uncategorized') return 'bg-muted text-muted-foreground';

    // Simple hash function for deterministic color
    const hash = collectionId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return COLLECTION_COLORS[hash % COLLECTION_COLORS.length];
  };

  // Create a new collection
  const handleCreateCollection = () => {
    if (!newCollectionName.trim()) return;

    const newCollection: ReferenceCollection = {
      id: nanoid(),
      name: newCollectionName.trim(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    addCollectionMutation.mutate(newCollection, {
      onSuccess: () => {
        toast({
          title: "Collection Created",
          description: `Collection "${newCollection.name}" has been created.`
        });
        setNewCollectionName('');
        setShowCollectionInput(false);
        refetchCollections();
      },
      onError: (error) => {
        toast({
          title: "Error Creating Collection",
          description: error.message,
          variant: "destructive"
        });
      }
    });
  };

  // Delete a collection
  const handleDeleteCollection = () => {
    if (!deleteCollection) return;

    deleteCollectionMutation.mutate(deleteCollection.id, {
      onSuccess: () => {
        toast({
          title: "Collection Deleted",
          description: `Collection "${deleteCollection.name}" has been deleted.`
        });

        // If the active collection is being deleted, switch to 'all'
        if (activeCollection === deleteCollection.id) {
          setActiveCollection('all');
        }

        setDeleteCollection(null);
        setDeleteModalOpen(false);
        refetchCollections();
      },
      onError: (error) => {
        toast({
          title: "Error Deleting Collection",
          description: error.message,
          variant: "destructive"
        });
      }
    });
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: string) => {
    if (field === 'authors') {
      // Convert comma-separated authors to array
      setReferenceFields(prev => ({
        ...prev,
        authors: value.split(',').map(author => author.trim())
      }));
    } else {
      setReferenceFields(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Add a new reference
  const handleAddReference = () => {
    // Create ID if not editing
    const id = editingReference ? editingReference.id : nanoid();

    // Create reference object
    const newReference: Reference = {
      ...referenceFields as Reference,
      id,
      type: referenceFields.type || 'book',
      authors: referenceFields.authors || [],
      title: referenceFields.title || '',
      createdAt: editingReference?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (editingReference) {
      // Update existing reference
      updateReferenceMutation.mutate(newReference, {
        onSuccess: () => {
          toast({
            title: "Reference Updated",
            description: `Reference "${newReference.title}" has been updated.`
          });
          resetForm();
          setActiveTab('library');
          refetchReferences();
        },
        onError: (error) => {
          toast({
            title: "Error Updating Reference",
            description: error.message,
            variant: "destructive"
          });
        }
      });
    } else {
      // Add new reference
      addReferenceMutation.mutate(newReference, {
        onSuccess: (savedReference) => {
          // Also add to document-specific references for backward compatibility
          onAddReference({
            id: savedReference.id,
            marker: `[${allReferences.length + 1}]`,
            reference: formatCitation({
              ...savedReference,
              marker: "",
              reference: ""
            } as any, 'apa')
          });

          toast({
            title: "Reference Added",
            description: `Reference "${newReference.title}" has been added.`
          });
          resetForm();
          setActiveTab('library');
          refetchReferences();
        },
        onError: (error) => {
          toast({
            title: "Error Adding Reference",
            description: error.message,
            variant: "destructive"
          });
        }
      });
    }
  };

  // Delete a reference
  const handleDeleteReference = (referenceId: string) => {
    if (!confirm("Are you sure you want to delete this reference?")) return;

    deleteReferenceMutation.mutate(referenceId, {
      onSuccess: () => {
        // Also delete from document-specific references
        onDeleteReference(referenceId);

        toast({
          title: "Reference Deleted",
          description: "The reference has been deleted."
        });
        refetchReferences();
      },
      onError: (error) => {
        toast({
          title: "Error Deleting Reference",
          description: error.message,
          variant: "destructive"
        });
      }
    });
  };

  // Edit an existing reference
  const handleEditReference = (reference: Reference) => {
    setEditingReference(reference);
    setReferenceFields({
      ...reference,
      authors: reference.authors
    });
    setActiveTab('manual');
    setShowBackButton(true);
  };

  // Assign a reference to a collection
  const assignToCollection = (referenceId: string, collectionId: string) => {
    // Find the reference
    const reference = (globalReferences as Reference[]).find(r => r.id === referenceId);
    if (!reference) return;
    
    // Update the reference with the new collection ID (or undefined if 'none')
    const updatedReference = {
      ...reference,
      collectionId: collectionId === 'none' ? undefined : collectionId,
      updatedAt: new Date().toISOString()
    };
    
    updateReferenceMutation.mutate(updatedReference, {
      onSuccess: () => {
        toast({
          title: "Reference Updated",
          description: `Reference assigned to ${
            collectionId === 'none'
              ? 'no collection'
              : (collections.find(c => c.id === collectionId)?.name || 'collection')
          }.`
        });
        refetchReferences();
      },
      onError: (error) => {
        toast({
          title: "Error Updating Reference",
          description: error.message,
          variant: "destructive"
        });
      }
    });
  };
  
  // Import references from BibTeX or RIS
  const handleImport = () => {
    if (!importText.trim()) {
      toast({
        title: "Import Text Required",
        description: "Please paste BibTeX or RIS text to import",
        variant: "destructive"
      });
      return;
    }
    
    try {
      let importedReferences: Partial<Reference>[] = [];
      
      if (importFormat === 'bibtex') {
        importedReferences = parseBibTeX(importText);
      } else if (importFormat === 'ris') {
        importedReferences = parseRIS(importText);
      }
      
      if (importedReferences.length === 0) {
        toast({
          title: "No References Found",
          description: "No valid references were found in the import text",
          variant: "destructive"
        });
        return;
      }
      
      // Add IDs and timestamps
      const now = new Date().toISOString();
      const referencesWithIds = importedReferences.map(ref => ({
        ...ref,
        id: nanoid(),
        type: ref.type || 'other',
        title: ref.title || 'Untitled Reference',
        authors: ref.authors || [],
        createdAt: now,
        updatedAt: now
      }));
      
      // Add each reference
      const addPromises = referencesWithIds.map(ref =>
        addReferenceMutation.mutateAsync(ref as Reference)
          .then(savedRef => {
            // Also add to document-specific references
            onAddReference({
              id: savedRef.id,
              marker: `[${allReferences.length + 1}]`,
              reference: formatCitation({
                ...savedRef,
                marker: "",
                reference: ""
              } as any, 'apa')
            });
            return savedRef;
          })
      );

      Promise.all(addPromises)
        .then(savedRefs => {
          toast({
            title: "References Imported",
            description: `${savedRefs.length} references have been imported.`
          });
          setImportText('');
          setActiveTab('library');
          refetchReferences();
        })
        .catch(error => {
          toast({
            title: "Import Error",
            description: error.message,
            variant: "destructive"
          });
        });
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description: "Could not parse the import text",
        variant: "destructive"
      });
    }
  };
  
  // Look up a reference by ISBN
  const handleISBNLookup = async () => {
    const isbnClean = isbnQuery.replace(/-/g, '').trim();
    
    if (!isbnClean) {
      toast({
        title: "ISBN Required",
        description: "Please enter an ISBN to search",
        variant: "destructive"
      });
      return;
    }
    
    setIsbnLoading(true);
    
    try {
      // Use the Open Library API to look up ISBN
      const response = await fetch(`https://openlibrary.org/isbn/${isbnClean}.json`);

      if (!response.ok) {
        throw new Error("Book not found");
      }

      const data = await response.json();

      // Get author names if available
      let authors: string[] = [];
      if (data.authors) {
        const authorPromises = data.authors.map(async (author: any) => {
          const authorKey = author.key;
          const authorResponse = await fetch(`https://openlibrary.org${authorKey}.json`);
          if (!authorResponse.ok) return 'Unknown Author';
          const authorData = await authorResponse.json();
          return authorData.name || 'Unknown Author';
        });

        authors = await Promise.all(authorPromises);
      }

      // Get publication date
      const year = data.publish_date ? data.publish_date.replace(/^.*(\d{4}).*$/, '$1') : '';

      // Create new reference
      const newReference: Reference = {
        id: nanoid(),
        type: 'book',
        title: data.title || 'Untitled Book',
        authors: authors.length ? authors : ['Unknown Author'],
        year: year,
        publisher: data.publishers?.[0] || '',
        url: `https://openlibrary.org/isbn/${isbnClean}`,
        accessed: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      addReferenceMutation.mutate(newReference, {
        onSuccess: (savedReference) => {
          // Also add to document-specific references
          onAddReference({
            id: savedReference.id,
            marker: `[${allReferences.length + 1}]`,
            reference: formatCitation({
              ...savedReference,
              marker: "",
              reference: ""
            } as any, 'apa')
          });

          toast({
            title: "Reference Added",
            description: `Book "${data.title}" has been added.`
          });
          setIsbnQuery('');
          setActiveTab('library');
          refetchReferences();
        },
        onError: (error) => {
          toast({
            title: "Error Adding Reference",
            description: error.message,
            variant: "destructive"
          });
        }
      });
    } catch (error) {
      console.error("ISBN lookup error:", error);
      toast({
        title: "ISBN Lookup Failed",
        description: "Could not find a book with this ISBN",
        variant: "destructive"
      });
    } finally {
      setIsbnLoading(false);
    }
  };
  
  // Format a reference as a citation
  const formatReferenceAsCitation = (reference: Reference | Partial<Reference>, style: string = 'apa'): string => {
    if (!reference.title || !reference.authors?.length) {
      return "Fill in the required fields to see a preview";
    }
    
    // Convert string to CitationStyle type
    const citationStyle: CitationStyle = (style === 'apa' || style === 'mla' || style === 'chicago' ||
                                         style === 'harvard' || style === 'ieee')
      ? style as CitationStyle
      : 'apa';
    
    // This uses the utility function to format citations
    return formatCitation({
      ...reference,
      marker: "", // No marker for preview
      reference: ""
    } as any, citationStyle);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {showTrigger && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            References
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="max-w-[900px] max-h-[85vh] p-0 gap-0 bg-card text-card-foreground">
        <DialogHeader className="p-4 border-b border-border">
          <DialogTitle>Reference Manager</DialogTitle>
        </DialogHeader>
        
        <div className="flex h-[calc(85vh-60px)]">
          {/* Left Sidebar - Collections */}
          <div className="w-52 border-r border-border p-4 flex flex-col">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Collections</h3>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full"
                onClick={() => setShowCollectionInput(true)}
              >
                <span className="text-lg">+</span>
              </Button>
            </div>

            <ScrollArea className="flex-1">
              {collections.map(collection => (
                <div 
                  key={collection.id}
                  className={`
                    group rounded px-2 py-1 my-1 cursor-pointer flex justify-between items-center
                    ${activeCollection === collection.id ? 'bg-accent' : 'hover:bg-accent/50'}
                  `}
                  onClick={() => setActiveCollection(collection.id)}
                >
                  <span className="text-sm truncate">{collection.name}</span>
                  {collection.id !== 'all' && collection.id !== 'uncategorized' && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        setDeleteCollection(collection);
                        setDeleteModalOpen(true);
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </Button>
                  )}
                </div>
              ))}

              {showCollectionInput && (
                <div className="mt-2 space-y-2">
                  <Input
                    placeholder="Collection name"
                    value={newCollectionName}
                    onChange={(e) => setNewCollectionName(e.target.value)}
                    className="h-8 bg-input"
                  />
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={handleCreateCollection}
                      disabled={!newCollectionName.trim()}
                    >
                      Save
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setShowCollectionInput(false);
                        setNewCollectionName('');
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col h-full">
            {/* Tabs Nav */}
            <div className="border-b border-border flex h-14">
              {showBackButton && (
                <Button
                  variant="ghost"
                  className="px-3 rounded-none h-full"
                  onClick={() => {
                    setActiveTab('library');
                    setShowBackButton(false);
                    resetForm();
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Back
                </Button>
              )}

              <nav className="flex ml-4">
                <button
                  className={`px-4 py-2 h-full border-b-2 ${activeTab === 'library' ? 'border-blue-500 text-blue-500' : 'border-transparent hover:text-muted-foreground'}`}
                  onClick={() => setActiveTab('library')}
                >
                  Library
                </button>
                <button
                  className={`px-4 py-2 h-full border-b-2 ${activeTab === 'manual' ? 'border-blue-500 text-blue-500' : 'border-transparent hover:text-muted-foreground'}`}
                  onClick={() => {
                    setActiveTab('manual');
                    resetForm();
                  }}
                >
                  Manual Entry
                </button>
                <button
                  className={`px-4 py-2 h-full border-b-2 ${activeTab === 'import' ? 'border-blue-500 text-blue-500' : 'border-transparent hover:text-muted-foreground'}`}
                  onClick={() => setActiveTab('import')}
                >
                  Import BibTeX/RIS
                </button>
                <button
                  className={`px-4 py-2 h-full border-b-2 ${activeTab === 'isbn' ? 'border-blue-500 text-blue-500' : 'border-transparent hover:text-muted-foreground'}`}
                  onClick={() => setActiveTab('isbn')}
                >
                  ISBN Lookup
                </button>
              </nav>
            </div>

            {/* Content Area */}
            <div className="flex-1 overflow-hidden">
              {/* Library Tab */}
              {activeTab === 'library' && (
                <div className="h-full flex flex-col">
                  <div className="p-4 border-b border-border flex flex-wrap gap-2">
                    <div className="flex-1 min-w-[200px]">
                      <Input
                        placeholder="Search references..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full bg-input border-input"
                      />
                    </div>
                    <div className="w-48">
                      <Select value={filterType} onValueChange={setFilterType}>
                        <SelectTrigger className="bg-input border-input">
                          <SelectValue placeholder="Filter by type" />
                        </SelectTrigger>
                        <SelectContent className="bg-popover border-border">
                          <SelectItem value="all_types">All Types</SelectItem>
                          {referenceTypes.map(type => (
                            <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <ScrollArea className="flex-1 p-4">
                    {isLoadingReferences ? (
                      <div className="text-center py-8">Loading references...</div>
                    ) : filteredReferences.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        {searchQuery || filterType !== 'all_types' || activeCollection !== 'all'
                          ? "No references match your filter criteria."
                          : "No references yet. Add references using the tabs above."}
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {filteredReferences.map(reference => (
                          <div key={reference.id} className="bg-accent/40 rounded-md p-3 space-y-2">
                            <div className="flex justify-between">
                              <div>
                                <h4 className="font-medium">{reference.title}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {reference.authors?.join(', ')}
                                  {reference.year && ` • ${reference.year}`}
                                  {reference.journal && ` • ${reference.journal}`}
                                </p>
                              </div>
                              <div className="flex space-x-1">
                                {reference.collectionId && (
                                  <span className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getCollectionColor(reference.collectionId)}`}>
                                    {collections.find(c => c.id === reference.collectionId)?.name || 'Collection'}
                                  </span>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() => handleEditReference(reference)}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                  </svg>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 text-red-500 hover:text-red-400"
                                  onClick={() => handleDeleteReference(reference.id)}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </Button>
                              </div>
                            </div>

                            <div className="bg-muted text-muted-foreground p-2 rounded text-sm">
                              {formatReferenceAsCitation(reference)}
                            </div>

                            <div className="flex justify-between items-center pt-1">
                              <Select
                                value={reference.collectionId || "none"}
                                onValueChange={(value) => assignToCollection(reference.id, value)}
                              >
                                <SelectTrigger className="w-44 h-8 text-xs bg-muted border-input">
                                  <SelectValue placeholder="Assign to collection" />
                                </SelectTrigger>
                                <SelectContent className="bg-popover border-border">
                                  <SelectItem value="none">None</SelectItem>
                                  {collections
                                    .filter(c => c.id !== 'all' && c.id !== 'uncategorized')
                                    .map(collection => (
                                      <SelectItem key={collection.id} value={collection.id}>
                                        {collection.name}
                                      </SelectItem>
                                    ))
                                  }
                                </SelectContent>
                              </Select>


                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
              )}

              {/* Manual Entry Tab */}
              {activeTab === 'manual' && (
                <ScrollArea className="h-full p-4">
                  <div className="space-y-4 pb-16">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <Label htmlFor="type">Reference Type</Label>
                        <Select
                          value={referenceFields.type || 'book'}
                          onValueChange={(value) => handleInputChange('type', value)}
                        >
                          <SelectTrigger id="type" className="bg-input border-input">
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent className="bg-popover border-border">
                            {referenceTypes.map(type => (
                              <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-1">
                        <Label htmlFor="title">Title</Label>
                        <Input
                          id="title"
                          placeholder="Title of the work"
                          value={referenceFields.title || ''}
                          onChange={(e) => handleInputChange('title', e.target.value)}
                          className="bg-input border-input"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label htmlFor="authors">Authors (comma separated)</Label>
                        <Input
                          id="authors"
                          placeholder="Author 1, Author 2, etc."
                          value={referenceFields.authors?.join(', ') || ''}
                          onChange={(e) => handleInputChange('authors', e.target.value)}
                          className="bg-input border-input"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label htmlFor="year">Year</Label>
                        <Input
                          id="year"
                          placeholder="Publication year"
                          value={referenceFields.year || ''}
                          onChange={(e) => handleInputChange('year', e.target.value)}
                          className="bg-input border-input"
                        />
                      </div>

                      {/* Fields for journal articles */}
                      {referenceFields.type === 'journal' && (
                        <>
                          <div className="space-y-1">
                            <Label htmlFor="journal">Journal</Label>
                            <Input
                              id="journal"
                              placeholder="Journal name"
                              value={referenceFields.journal || ''}
                              onChange={(e) => handleInputChange('journal', e.target.value)}
                              className="bg-input border-input"
                            />
                          </div>

                          <div className="space-y-1">
                            <Label htmlFor="volume">Volume</Label>
                            <Input
                              id="volume"
                              placeholder="Volume number"
                              value={referenceFields.volume || ''}
                              onChange={(e) => handleInputChange('volume', e.target.value)}
                              className="bg-input border-input"
                            />
                          </div>

                          <div className="space-y-1">
                            <Label htmlFor="issue">Issue</Label>
                            <Input
                              id="issue"
                              placeholder="Issue number"
                              value={referenceFields.issue || ''}
                              onChange={(e) => handleInputChange('issue', e.target.value)}
                              className="bg-input border-input"
                            />
                          </div>

                          <div className="space-y-1">
                            <Label htmlFor="pages">Pages</Label>
                            <Input
                              id="pages"
                              placeholder="Page range (e.g., 123-145)"
                              value={referenceFields.pages || ''}
                              onChange={(e) => handleInputChange('pages', e.target.value)}
                              className="bg-input border-input"
                            />
                          </div>
                        </>
                      )}

                      {/* Fields for books */}
                      {referenceFields.type === 'book' && (
                        <>
                          <div className="space-y-1">
                            <Label htmlFor="publisher">Publisher</Label>
                            <Input
                              id="publisher"
                              placeholder="Publisher name"
                              value={referenceFields.publisher || ''}
                              onChange={(e) => handleInputChange('publisher', e.target.value)}
                              className="bg-input border-input"
                            />
                          </div>
                        </>
                      )}

                      <div className="space-y-1">
                        <Label htmlFor="doi">DOI</Label>
                        <Input
                          id="doi"
                          placeholder="Digital Object Identifier"
                          value={referenceFields.doi || ''}
                          onChange={(e) => handleInputChange('doi', e.target.value)}
                          className="bg-input border-input"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label htmlFor="url">URL</Label>
                        <Input
                          id="url"
                          placeholder="Web address"
                          value={referenceFields.url || ''}
                          onChange={(e) => handleInputChange('url', e.target.value)}
                          className="bg-input border-input"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label htmlFor="accessed">Access Date</Label>
                        <Input
                          id="accessed"
                          placeholder="Date accessed (YYYY-MM-DD)"
                          value={referenceFields.accessed || ''}
                          onChange={(e) => handleInputChange('accessed', e.target.value)}
                          className="bg-input border-input"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label htmlFor="collection">Collection</Label>
                        <Select
                          value={referenceFields.collectionId || "none"}
                          onValueChange={(value) => handleInputChange('collectionId', value === "none" ? "" : value)}
                        >
                          <SelectTrigger id="collection" className="bg-input border-input">
                            <SelectValue placeholder="Assign to collection" />
                          </SelectTrigger>
                          <SelectContent className="bg-input border-input">
                            <SelectItem value="none">None</SelectItem>
                            {collections
                              .filter(c => c.id !== 'all' && c.id !== 'uncategorized')
                              .map(collection => (
                                <SelectItem key={collection.id} value={collection.id}>
                                  {collection.name}
                                </SelectItem>
                              ))
                            }
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="bg-muted p-3 rounded-md mt-4">
                      <Label className="mb-2 block">Preview</Label>
                      <div className="bg-card p-3 rounded text-sm">
                        {formatReferenceAsCitation(referenceFields)}
                      </div>
                    </div>

                    <div className="flex justify-end gap-2 pt-4">
                      <Button
                        variant="outline"
                        onClick={() => {
                          resetForm();
                          setActiveTab('library');
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleAddReference}
                        disabled={!referenceFields.title || !referenceFields.authors?.length}
                      >
                        {editingReference ? 'Update Reference' : 'Add Reference'}
                      </Button>
                    </div>
                  </div>
                </ScrollArea>
              )}
              
              {/* Import Tab */}
              {activeTab === 'import' && (
                <div className="h-full flex flex-col p-4">
                  <div className="flex items-center mb-4">
                    <h3 className="font-medium mr-4">Import Format</h3>
                    <div className="flex">
                      <button
                        className={`px-3 py-1 text-sm rounded-l ${
                          importFormat === 'bibtex' ? 'bg-blue-600 text-white' : 'bg-gray-700 hover:bg-gray-600'
                        }`}
                        onClick={() => setImportFormat('bibtex')}
                      >
                        BibTeX
                      </button>
                      <button
                        className={`px-3 py-1 text-sm rounded-r ${
                          importFormat === 'ris' ? 'bg-blue-600 text-white' : 'bg-gray-700 hover:bg-gray-600'
                        }`}
                        onClick={() => setImportFormat('ris')}
                      >
                        RIS
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-2">
                    Paste {importFormat === 'bibtex' ? 'BibTeX' : 'RIS'} references from your reference manager.
                  </p>

                  <Textarea
                    placeholder={`Paste your ${importFormat === 'bibtex' ? 'BibTeX' : 'RIS'} references here...`}
                    value={importText}
                    onChange={(e) => setImportText(e.target.value)}
                    className="flex-1 bg-input border-input text-sm"
                  />

                  <div className="flex justify-between items-center mt-4 pt-4 border-t border-border">
                    <div className="text-xs text-muted-foreground">
                      {importFormat === 'bibtex'
                        ? "Export BibTeX from Zotero, Mendeley, EndNote, or other reference managers."
                        : "Export RIS from Zotero, Mendeley, EndNote, or other reference managers."}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setImportText('');
                          setActiveTab('library');
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleImport}
                        disabled={!importText.trim()}
                      >
                        Import References
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              
              {/* ISBN Lookup Tab */}
              {activeTab === 'isbn' && (
                <div className="p-4">
                  <h3 className="font-medium mb-4">ISBN Lookup</h3>
                  
                  <div className="flex gap-2 mb-4">
                    <Input
                      placeholder="Enter ISBN (e.g., 978-0-306-40615-7)"
                      value={isbnQuery}
                      onChange={(e) => setIsbnQuery(e.target.value)}
                      className="bg-input border-input"
                    />
                    <Button
                      onClick={handleISBNLookup}
                      disabled={isbnLoading || !isbnQuery.trim()}
                    >
                      {isbnLoading ? 'Searching...' : 'Search'}
                    </Button>
                  </div>
                  
                  <div className="bg-muted p-4 rounded-md mt-4">
                    <p className="text-sm">
                      Enter an ISBN to automatically fetch book information from the Open Library database.
                    </p>
                    <div className="mt-3 text-xs text-muted-foreground">
                      <p>You can find the ISBN:</p>
                      <ul className="list-disc pl-4 mt-1 space-y-1">
                        <li>On the back cover of most books, near the barcode</li>
                        <li>On the copyright page inside the book</li>
                        <li>Both ISBN-10 and ISBN-13 formats are supported</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>

      {/* Delete Collection Confirmation Modal */}
      {deleteModalOpen && deleteCollection && (
        <ConfirmModal
          isOpen={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setDeleteCollection(null);
          }}
          onConfirm={handleDeleteCollection}
          title="Delete Collection"
          description={`Are you sure you want to delete the "${deleteCollection.name}" collection? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
        />
      )}
    </Dialog>
  );
}