import React, { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// Select, SelectContent, SelectItem, SelectTrigger, SelectValue REMOVED
import { Note } from "@/lib/types"; // OutlineItem REMOVED (assuming not used elsewhere)
// Badge REMOVED
import { AiEnhancedRichTextEditor } from "@/components/ui/ai-enhanced-rich-text-editor";
import { AttachmentList } from "@/components/notes/attachments/attachment-list";
import { useToast } from "@/hooks/use-toast";

interface WysiwygNoteEditorProps {
  note: Note;
  // outlineItems: OutlineItem[]; // REMOVED
  onUpdate: (note: Note, forceSaveNow?: boolean) => void;
  onDelete: () => void;
  onFileUpload: (file: File) => Promise<string>;
  hideHeader?: boolean; // If true, don't show the title/delete header
  currentUserPermissionLevel?: 'view' | 'edit' | null;
  isDocumentOwner?: boolean;
}

export function WysiwygNoteEditor({
  note,
  // outlineItems, // REMOVED
  onUpdate,
  onDelete,
  onFileUpload,
  hideHeader = false,
  currentUserPermissionLevel,
  isDocumentOwner,
}: WysiwygNoteEditorProps) {
  const [isDraggingImage, setIsDraggingImage] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const isReadOnly = currentUserPermissionLevel === 'view';
  // markdownMode state seems unused based on current logic, consider removing if not planned
  // const [markdownMode, setMarkdownMode] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  // markdownEditorRef seems unused if markdownMode is not fully implemented
  // const markdownEditorRef = useRef<HTMLTextAreaElement>(null);
  const lastNoteIdRef = useRef<string | null>(null);
  const updateTimeoutRef = useRef<number | null>(null);
  const lastFileUploadRef = useRef<Note | null>(null);
  const { toast } = useToast();
  const [currentTitle, setCurrentTitle] = useState(note.title || ''); // New title state
  const [selectedText, setSelectedText] = useState<{
    text: string;
    range: Range | null;
  }>({ text: "", range: null });

  // Check if the note has any attachments
  const hasAttachments = () => {
    return (
      (note.imageUrls && note.imageUrls.length > 0) ||
      (note.videoUrls && note.videoUrls.length > 0) ||
      (note.fileUrls && note.fileUrls.length > 0) ||
      note.primaryAssetUrl
    );
  };

  // Function to get total number of attachments
  const getAttachmentCount = () => {
    const imageCount = note.imageUrls?.length || 0;
    const videoCount = note.videoUrls?.length || 0;
    const fileCount = note.fileUrls?.length || 0;
    const primaryCount = note.primaryAssetUrl ? 1 : 0;
    return imageCount + videoCount + fileCount + primaryCount;
  };

  // New title handlers
  useEffect(() => {
    setCurrentTitle(note.title || '');
  }, [note.title]);

  const handleTitleSave = () => {
    const trimmedTitle = currentTitle.trim();
    if (trimmedTitle !== (note.title || '')) {
      onUpdate({ ...note, title: trimmedTitle, updatedAt: new Date().toISOString() }, true);
    }
  };

  const handleTitleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleTitleSave();
      event.currentTarget.blur();
    }
  };

  // Original handleTitleChange (for the old input) is removed / commented out below.

  // handleOutlineLinkChange function REMOVED
  // flattenOutlineItems function removed
  // getLinkedOutlineItems function removed

  const handleImageDragOver = (e: React.DragEvent) => {
    if (isReadOnly) return;
    e.preventDefault();
    setIsDraggingImage(true);
  };

  const handleImageDragLeave = () => {
    if (isReadOnly) return;
    setIsDraggingImage(false);
  };

  const handleImageDrop = async (e: React.DragEvent) => {
    if (isReadOnly) return;
    e.preventDefault();
    setIsDraggingImage(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (
        file.type.startsWith("image/") ||
        file.type.startsWith("video/") ||
        file.type === "application/pdf" ||
        file.type === "application/msword" ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
        file.type === "text/plain" ||
        file.type === "text/markdown"
      ) {
        try {
          setIsUploading(true);
          const fileInputEl = fileInputRef.current;
          if (fileInputEl) {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInputEl.files = dataTransfer.files;
            await handleImageFileChange({
              target: { files: dataTransfer.files },
            } as React.ChangeEvent<HTMLInputElement>);
          }
        } catch (error) {
          console.error("Failed to process dropped file:", error);
        } finally {
          setIsUploading(false);
        }
      }
    }
  };

  const handleImageButtonClick = () => {
    if (isReadOnly) return;
    fileInputRef.current?.click();
  };

  const handleImageFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      try {
        setIsUploading(true);
        if (file.type.startsWith("image/")) {
          await insertImage(file);
        } else if (file.type.startsWith("video/")) {
          await insertVideo(file);
        } else if (
          file.type === "application/pdf" ||
          file.type === "application/msword" ||
          file.type ===
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          file.type === "text/plain" ||
          file.type === "text/markdown"
        ) {
          await insertDocument(file);
        }
      } catch (error) {
        console.error("Failed to handle file:", error);
      } finally {
        setIsUploading(false);
      }
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const insertImage = async (file: File) => {
    setIsUploading(true);
    try {
      const imageUrl = await onFileUpload(file);
      if (editorRef.current) {
        const attachmentData = {
          url: imageUrl,
          id: imageUrl.split("/").pop()?.split(".")[0] || "",
          type: "image",
          name: file.name,
          displayName: file.name,
          size: file.size,
          mimetype: file.type,
        };
        const updatedNote = {
          ...note,
          imageUrls: [...(note.imageUrls || []), imageUrl],
          imageUrlsData: [...(note.imageUrlsData || []), attachmentData],
          videoUrls: note.videoUrls || [],
          videoUrlsData: note.videoUrlsData || [],
          fileUrls: note.fileUrls || [],
          fileUrlsData: note.fileUrlsData || [],
          primaryAssetUrl: note.primaryAssetUrl,
          primaryAssetData: note.primaryAssetData,
          updatedAt: new Date().toISOString(),
        };
        lastFileUploadRef.current = updatedNote;
        onUpdate(updatedNote, true);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const insertVideo = async (file: File) => {
    setIsUploading(true);
    try {
      const videoUrl = await onFileUpload(file);
      if (editorRef.current) {
        const attachmentData = {
          url: videoUrl,
          id: videoUrl.split("/").pop()?.split(".")[0] || "",
          type: "video",
          name: file.name,
          displayName: file.name,
          size: file.size,
          mimetype: file.type,
        };
        const updatedNote = {
          ...note,
          videoUrls: [...(note.videoUrls || []), videoUrl],
          videoUrlsData: [...(note.videoUrlsData || []), attachmentData],
          imageUrls: note.imageUrls || [],
          imageUrlsData: note.imageUrlsData || [],
          fileUrls: note.fileUrls || [],
          fileUrlsData: note.fileUrlsData || [],
          primaryAssetUrl: note.primaryAssetUrl,
          primaryAssetData: note.primaryAssetData,
          updatedAt: new Date().toISOString(),
        };
        lastFileUploadRef.current = updatedNote;
        onUpdate(updatedNote, true);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const insertDocument = async (file: File) => {
    setIsUploading(true);
    try {
      const fileUrl = await onFileUpload(file);
      if (editorRef.current) {
        const attachmentData = {
          url: fileUrl,
          id: fileUrl.split("/").pop()?.split(".")[0] || "",
          type: "file",
          name: file.name,
          displayName: file.name,
          size: file.size,
          mimetype: file.type,
        };
        const updatedNote = {
          ...note,
          fileUrls: [...(note.fileUrls || []), fileUrl],
          fileUrlsData: [...(note.fileUrlsData || []), attachmentData],
          imageUrls: note.imageUrls || [],
          imageUrlsData: note.imageUrlsData || [],
          videoUrls: note.videoUrls || [],
          videoUrlsData: note.videoUrlsData || [],
          primaryAssetUrl: note.primaryAssetUrl,
          primaryAssetData: note.primaryAssetData,
          updatedAt: new Date().toISOString(),
        };
        lastFileUploadRef.current = updatedNote;
        onUpdate(updatedNote, true);
      }
    } catch (error) {
      console.error("Error uploading document:", error);
    } finally {
      setIsUploading(false);
    }
  };

  useEffect(() => {
    const handleAttachmentDeleted = (e: Event) => {
      const customEvent = e as CustomEvent;
      const updatedNoteData = customEvent.detail?.note; // Renamed to avoid conflict
      if (updatedNoteData) {
        onUpdate(updatedNoteData, true);
      }
    };
    document.addEventListener("attachment-deleted", handleAttachmentDeleted);
    return () => {
      document.removeEventListener("attachment-deleted", handleAttachmentDeleted);
    };
  }, [onUpdate]);

  useEffect(() => {
    const saveSelection = () => {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (editorRef.current?.contains(range.commonAncestorContainer)) {
          setSelectedText({
            text: selection.toString(),
            range: range.cloneRange(),
          });
        }
      }
    };
    document.addEventListener("selectionchange", saveSelection);
    return () => {
      document.removeEventListener("selectionchange", saveSelection);
    };
  }, []);

  useEffect(() => {
    if (editorRef.current) {
      if (
        editorRef.current.innerHTML === "" ||
        !editorRef.current.isContentEditable ||
        lastNoteIdRef.current !== note.id
      ) {
        const contentToSet = note.content.trim().startsWith("<")
          ? note.content
          : markdownToHtml(note.content); // Assuming markdownToHtml is kept or replaced
        editorRef.current.innerHTML = contentToSet;
        lastNoteIdRef.current = note.id;
      }
    }
    // markdownMode removed from dependencies
  }, [note.id, note.content]);

  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current !== null) {
        window.clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // markdownMode and related functions (handleMarkdownChange, htmlToMarkdown) are kept for now
  // as they might be used or re-introduced, but their direct UI (toggle) is removed.
  // If they are definitely not needed, they can be removed too.

  const updateNoteContent = (forceSave: boolean = false) => {
    if (!editorRef.current) return;
    const htmlContent = editorRef.current.innerHTML;
    const baseNote = lastFileUploadRef.current || note;
    const updatedNoteData = { // Renamed to avoid conflict
      ...baseNote,
      content: htmlContent,
      imageUrls: baseNote.imageUrls || [],
      imageUrlsData: baseNote.imageUrlsData || [],
      videoUrls: baseNote.videoUrls || [],
      videoUrlsData: baseNote.videoUrlsData || [],
      fileUrls: baseNote.fileUrls || [],
      fileUrlsData: baseNote.fileUrlsData || [],
      primaryAssetUrl: baseNote.primaryAssetUrl,
      primaryAssetData: baseNote.primaryAssetData,
      updatedAt: new Date().toISOString(),
    };
    if (lastFileUploadRef.current) {
      lastFileUploadRef.current = null;
    }
    onUpdate(updatedNoteData, forceSave);
  };

  // markdownToHtml and htmlToMarkdown functions are kept as they might be used by content loading/saving logic
  // If content is always HTML, these might be simplified or removed.
  function markdownToHtml(markdown: string) {
    if (!markdown) return "";
    let html = markdown
      .replace(/^### (.*$)/gm, "<h3>$1</h3>")
      .replace(/^## (.*$)/gm, "<h2>$1</h2>")
      .replace(/^# (.*$)/gm, "<h1>$1</h1>")
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/__(.*?)__/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      .replace(/_(.*?)_/g, "<em>$1</em>")
      .replace(/```(.+?)```/g, function (match, codeContent) {
        return (
          "<pre><code>" +
          codeContent.replace(/</g, "&lt;").replace(/>/g, "&gt;") +
          "</code></pre>"
        );
      })
      .replace(/`([^`]+)`/g, function (match, codeContent) {
        return (
          "<code>" +
          codeContent.replace(/</g, "&lt;").replace(/>/g, "&gt;") +
          "</code>"
        );
      })
      .replace(/^\> (.*$)/gm, "<blockquote>$1</blockquote>")
      .replace(/^\-\-\-$/gm, "<hr>")
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
      .replace(
        /!\[([^\]]+)\]\(([^)]+)\)/g,
        '<span class="italic text-neutral-500 text-sm">[Image: $1 - Available in attachments]</span>',
      )
      .replace(/\n\s*\n/g, "\n<p-break>\n")
      .replace(/((^|\n)\- .*(\n\- .*)*)/g, function (match) {
        return (
          "<ul>" + match.replace(/^[\-\*] (.*$)/gm, "<li>$1</li>") + "</ul>"
        );
      })
      .replace(/((^|\n)\d+\. .*(\n\d+\. .*)*)/g, function (match) {
        return (
          "<ol>" + match.replace(/^\d+\. (.*$)/gm, "<li>$1</li>") + "</ol>"
        );
      });
    html = html.replace(/<p-break>/g, "</p><p>");
    html = "<p>" + html + "</p>";
    html = html.replace(/<p>\s*<\/p>/g, "");
    html = html.replace(/<p>(\s*<p>)/g, "$1");
    html = html.replace(/<\/p>(\s*<\/p>)/g, "$1");
    html = html.replace(
      /\n(?!<\/?(h[1-6]|blockquote|pre|li|ul|ol|p))/g,
      "<br>",
    );
    return html;
  }

  const getWordCount = () => {
    // markdownMode check removed as UI toggle is removed
    if (editorRef.current) {
      const text = editorRef.current.innerText || "";
      return text
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
    }
    return 0;
  };

  // typeIcon and typeColors are no longer used here as the outline linking UI is removed
  // const typeIcon = "ri-file-text-line";
  // const typeColors = { ... };

  return (
    <div className="flex flex-col h-full p-2 bg-popover text-popover-foreground rounded-lg shadow">

      {/* Section 1: Title Input */}
      <div className="flex items-center justify-between mb-2 flex-shrink-0">
        <Input
          type="text"
          placeholder="Note title..."
          value={currentTitle}
          onChange={(e) => !isReadOnly && setCurrentTitle(e.target.value)}
          onBlur={handleTitleSave}
          onKeyDown={handleTitleKeyDown}
          className={`text-lg font-medium border-0 border-b-2 border-primary/20 rounded-none pb-1 focus:ring-0 focus-visible:ring-0 focus:border-primary shadow-none w-full bg-transparent ${isReadOnly ? 'cursor-default opacity-75' : ''}`}
          readOnly={isReadOnly}
          disabled={isReadOnly}
        />
        {!hideHeader && !isReadOnly && (
          <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive hover:text-destructive-foreground hover:bg-destructive ml-2" onClick={onDelete} title="Delete note">
            <i className="ri-delete-bin-line"></i>
          </Button>
        )}
      </div>

      {/* Outline linking - only show if header is not hidden (i.e. in full editor view) */}
      {/* ENTIRE BLOCK REMOVED
      {!hideHeader && (
        ...
      )}
      */}

      {/* Section 2: Original content of WysiwygNoteEditor (excluding old title) */}
      <div className="flex-grow overflow-auto">
        {/* The original root was a Fragment, so we take its direct children.
            The first child of the fragment was the div that started with class "flex-1 p-4..."
            We also need to keep the drag handlers for images on a suitable wrapper if they were on the original root.
            The original root was <>, then <div className="flex-1 p-4...">
            The new structure is: NewRootDiv -> TitleDiv -> FlexGrowDivForOriginalContent -> OriginalFlex1P4Div
        */}
        <div
          className={`flex-1 p-4 overflow-y-auto relative flex flex-col ${isDraggingImage ? "bg-primary/5 border-primary/20" : ""}`}
          onDragOver={handleImageDragOver}
          onDragLeave={handleImageDragLeave}
          onDrop={handleImageDrop} // This was on the original root, now on the immediate wrapper of content
        >
          {/* Hidden file input for image uploads (part of original content) */}
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.md" // Keep original accept from this file
            onChange={handleImageFileChange}
            disabled={isUploading || isReadOnly}
          />

          {/* Original conditional header for title is REMOVED as it's replaced by the new title input above */}
          {/* {!hideHeader && (
            <div className="sticky top-0 z-10 bg-background border-neutral-200 dark:border-[#333333] pb-3">
              ... old title input ...
            </div>
          )} */}

          {/* Outline linking UI was removed from this file, so no need to include it here */}

          {/* Main content area: Rich Text Editor, Attachment Drop Zone, Attachment List */}
          <div className="flex flex-col h-full overflow-hidden"> {/* This was the next major div in original */}
            <div className="flex-grow overflow-auto mb-2">
              <AiEnhancedRichTextEditor
                value={note.content}
                onChange={(content: string) => { // Using the more specific onChange from this file
                  if (isReadOnly) return; // Prevent changes in read-only mode
                  if (updateTimeoutRef.current !== null) {
                    window.clearTimeout(updateTimeoutRef.current);
                  }
                  updateTimeoutRef.current = window.setTimeout(() => {
                    const updatedNote = {
                      ...note, // Use the base note prop
                      content,
                      // Preserve existing attachment arrays when only content changes
                      imageUrls: note.imageUrls || [],
                      imageUrlsData: note.imageUrlsData || [],
                      videoUrls: note.videoUrls || [],
                      videoUrlsData: note.videoUrlsData || [],
                      fileUrls: note.fileUrls || [],
                      fileUrlsData: note.fileUrlsData || [],
                      primaryAssetUrl: note.primaryAssetUrl,
                      primaryAssetData: note.primaryAssetData,
                      updatedAt: new Date().toISOString(),
                    };
                    onUpdate(updatedNote, false); // forceSaveNow is false for content changes
                  }, 500);
                }}
                className="border border-neutral-200 dark:border-neutral-700 rounded-md min-h-[200px] h-full"
                onShowSubscriptionModal={() => {
                  toast({
                    title: "AI Features",
                    description: "AI suggestions are available in the editor",
                    duration: 3000,
                  });
                }}
                readOnly={isReadOnly}
              />
              <div ref={editorRef} className="hidden" /> {/* Keep this ref */}
            </div>

            {/* Attachment Drop Zone - Hide when read-only */}
            {!isReadOnly && (
              <div
                className={`border border-dashed h-10 flex-shrink-0 mt-2 ${
                  isDraggingImage ? "border-primary bg-primary/10" : isUploading ? "border-primary bg-primary/5" : "border-border"
                } rounded-md p-1 text-center transition-colors cursor-pointer flex items-center justify-center`}
                // onClick for this dropzone was: onClick={isUploading ? undefined : handleImageButtonClick}
                // Re-evaluate if fileInputRef for general attachments should be different from content images
                // For now, let's assume it can use the same button click logic if desired, or be purely DND.
                // The prompt asks to move original content, this includes its interaction patterns.
                onClick={isUploading ? undefined : handleImageButtonClick}
              >
              {isUploading ? (
                <div className="flex items-center">
                  <i className="ri-loader-2-line animate-spin text-xl text-primary mr-2"></i>
                  <p className="text-sm text-primary">Uploading attachment...</p>
                </div>
              ) : (
                <div className="flex items-center">
                  <i className="ri-attachment-2 text-sm text-foreground/70 mr-1"></i>
                  <p className="text-xs text-foreground/70">
                    {hasAttachments() ? "Drop files or click to add more" : "Drop files or click to attach"}
                  </p>
                </div>
              )}
              {/* The file input is now at the top level of the original content wrapper */}
              </div>
            )}

            {/* Attachment List */}
            <div className="mt-1 flex-shrink-0 mb-1" style={{ maxHeight: "30%" }}>
              <h3 className="text-sm font-medium text-foreground mb-1">
                Attachments ({getAttachmentCount()})
              </h3>
              {hasAttachments() ? (
                <div
                  className="overflow-y-auto pr-1 border border-input dark:border-neutral-700 rounded-md p-2 bg-background"
                  style={{ maxHeight: hasAttachments() ? "120px" : "auto" }}
                >
                  <AttachmentList
                    note={note}
                    onDelete={(type, url) => {
                      const updatedNote = { ...note };
                      if (type === "image") {
                        const imageUrlIndex = (updatedNote.imageUrls || []).indexOf(url);
                        if (imageUrlIndex >= 0) {
                          updatedNote.imageUrls = (updatedNote.imageUrls || []).filter((u) => u !== url);
                          updatedNote.imageUrlsData = (updatedNote.imageUrlsData || []).filter((_, i) => i !== imageUrlIndex);
                        }
                      } else if (type === "video") {
                        const videoUrlIndex = (updatedNote.videoUrls || []).indexOf(url);
                        if (videoUrlIndex >= 0) {
                          updatedNote.videoUrls = (updatedNote.videoUrls || []).filter((u) => u !== url);
                          updatedNote.videoUrlsData = (updatedNote.videoUrlsData || []).filter((_, i) => i !== videoUrlIndex);
                        }
                      } else if (type === "file") {
                        const fileUrlIndex = (updatedNote.fileUrls || []).indexOf(url);
                        if (fileUrlIndex >= 0) {
                          updatedNote.fileUrls = (updatedNote.fileUrls || []).filter((u) => u !== url);
                          updatedNote.fileUrlsData = (updatedNote.fileUrlsData || []).filter((_, i) => i !== fileUrlIndex);
                        }
                      } else if (type === "primary") {
                        updatedNote.primaryAssetUrl = undefined;
                        updatedNote.primaryAssetData = undefined;
                      }
                      const getAttachmentName = (url: string): string => {
                        let attachmentName = "";
                        if (type === "image" && updatedNote.imageUrlsData) {
                          const data = updatedNote.imageUrlsData.find((d) => d.url === url);
                          if (data && data.displayName) attachmentName = data.displayName;
                        } else if (type === "video" && updatedNote.videoUrlsData) {
                          const data = updatedNote.videoUrlsData.find((d) => d.url === url);
                          if (data && data.displayName) attachmentName = data.displayName;
                        } else if (type === "file" && updatedNote.fileUrlsData) {
                          const data = updatedNote.fileUrlsData.find((d) => d.url === url);
                          if (data && data.displayName) attachmentName = data.displayName;
                        }
                        if (!attachmentName) attachmentName = url.split("/").pop() || "File";
                        return attachmentName;
                      };
                      const fileId = url.split("/").pop()?.split(".")[0];
                      const attachmentName = getAttachmentName(url);
                      if (fileId) {
                        toast({ title: "Deleting attachment", description: `Removing ${attachmentName}...`, duration: 2000 });
                        fetch(`/api/uploads/${fileId}`, { method: "DELETE" })
                          .then((response) => {
                            if (!response.ok) {
                              console.error(`Failed to delete file ${fileId} from server:`, response.statusText);
                              toast({ title: "Error", description: `Failed to remove ${attachmentName} from server.`, variant: "destructive", duration: 3000 });
                            } else {
                              console.log(`Successfully deleted file ${fileId} from server`);
                              toast({ title: "Attachment deleted", description: `Successfully removed ${attachmentName}.`, duration: 2000 });
                            }
                          })
                          .catch((error) => {
                            console.error(`Error deleting file ${fileId} from server:`, error);
                            toast({ title: "Error", description: `Failed to remove ${attachmentName} from server.`, variant: "destructive", duration: 3000 });
                          });
                      }
                      const event = new CustomEvent("attachment-deleted", { detail: { note: updatedNote, type, url } });
                      document.dispatchEvent(event);
                      onUpdate(updatedNote, true);
                    }}
                  />
                </div>
              ) : (
                <div className="text-sm text-foreground/75 border border-dashed border-input dark:border-neutral-700 rounded-md p-3 text-center">
                  No attachments yet. Drag and drop or click the area below to add files.
                </div>
              )}
            </div>
          </div>

          {/* Original Word Count Section - this was outside the main flex-col h-full div for editor content */}
          {/* It's now part of the content being moved. If !hideHeader, it should be visible. */}
          {!hideHeader && (
             <div className="p-2 border-t border-neutral-200 dark:border-neutral-700 flex justify-end items-center flex-shrink-0">
               <div className="text-sm text-neutral-500 dark:text-neutral-400">{getWordCount()} words</div>
             </div>
          )}
        </div>
      </div>
    </div>
  );
}
